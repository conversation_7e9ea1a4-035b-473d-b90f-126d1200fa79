"""
Data utility functions for the trading system
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import os

def validate_ohlcv_data(df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    Validate OHLCV data integrity
    
    Args:
        df: DataFrame with OHLCV data
        
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    # Check required columns
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        issues.append(f"Missing columns: {missing_columns}")
    
    if df.empty:
        issues.append("DataFrame is empty")
        return False, issues
    
    # Check for negative values
    for col in ['open', 'high', 'low', 'close', 'volume']:
        if col in df.columns and (df[col] < 0).any():
            issues.append(f"Negative values found in {col}")
    
    # Check OHLC relationships
    if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
        # High should be >= Open, Close
        if (df['high'] < df['open']).any() or (df['high'] < df['close']).any():
            issues.append("High price is less than Open or Close")
        
        # Low should be <= Open, Close
        if (df['low'] > df['open']).any() or (df['low'] > df['close']).any():
            issues.append("Low price is greater than Open or Close")
    
    # Check for missing values
    if df.isnull().any().any():
        null_columns = df.columns[df.isnull().any()].tolist()
        issues.append(f"Missing values in columns: {null_columns}")
    
    # Check for duplicate timestamps
    if df.index.duplicated().any():
        issues.append("Duplicate timestamps found")
    
    return len(issues) == 0, issues

def clean_ohlcv_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean OHLCV data by handling common issues
    
    Args:
        df: Raw OHLCV DataFrame
        
    Returns:
        Cleaned DataFrame
    """
    df_clean = df.copy()
    
    # Remove duplicates
    df_clean = df_clean[~df_clean.index.duplicated(keep='first')]
    
    # Sort by timestamp
    df_clean = df_clean.sort_index()
    
    # Forward fill missing values (conservative approach)
    df_clean = df_clean.fillna(method='ffill')
    
    # Remove any remaining NaN rows
    df_clean = df_clean.dropna()
    
    # Ensure positive values
    numeric_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in numeric_columns:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].abs()
    
    # Fix OHLC relationships
    if all(col in df_clean.columns for col in ['open', 'high', 'low', 'close']):
        # Ensure High is the maximum of O, H, L, C
        df_clean['high'] = df_clean[['open', 'high', 'low', 'close']].max(axis=1)
        
        # Ensure Low is the minimum of O, H, L, C
        df_clean['low'] = df_clean[['open', 'high', 'low', 'close']].min(axis=1)
    
    return df_clean

def resample_data(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    Resample OHLCV data to different timeframe
    
    Args:
        df: OHLCV DataFrame
        timeframe: Target timeframe ('5T', '15T', '1H', '4H', '1D', etc.)
        
    Returns:
        Resampled DataFrame
    """
    if df.empty:
        return df
    
    # Define aggregation rules
    agg_rules = {
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }
    
    # Only use columns that exist
    available_agg = {k: v for k, v in agg_rules.items() if k in df.columns}
    
    resampled = df.resample(timeframe).agg(available_agg)
    
    # Remove incomplete periods (last row if it's partial)
    if not resampled.empty:
        resampled = resampled.dropna()
    
    return resampled

def calculate_returns(df: pd.DataFrame, price_column: str = 'close') -> pd.DataFrame:
    """
    Calculate various return metrics
    
    Args:
        df: DataFrame with price data
        price_column: Column to use for return calculation
        
    Returns:
        DataFrame with return columns added
    """
    result_df = df.copy()
    
    if price_column not in df.columns:
        return result_df
    
    prices = df[price_column]
    
    # Simple returns
    result_df['returns'] = prices.pct_change()
    
    # Log returns
    result_df['log_returns'] = np.log(prices / prices.shift(1))
    
    # Cumulative returns
    result_df['cumulative_returns'] = (1 + result_df['returns']).cumprod() - 1
    
    # Rolling volatility (20-period)
    result_df['volatility_20'] = result_df['returns'].rolling(20).std()
    
    return result_df

def detect_outliers(df: pd.DataFrame, columns: List[str] = None, method: str = 'iqr') -> pd.DataFrame:
    """
    Detect outliers in data
    
    Args:
        df: DataFrame to analyze
        columns: Columns to check for outliers
        method: Method to use ('iqr', 'zscore')
        
    Returns:
        DataFrame with outlier flags
    """
    if columns is None:
        columns = ['open', 'high', 'low', 'close', 'volume']
    
    result_df = df.copy()
    
    for col in columns:
        if col not in df.columns:
            continue
        
        if method == 'iqr':
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            result_df[f'{col}_outlier'] = (df[col] < lower_bound) | (df[col] > upper_bound)
            
        elif method == 'zscore':
            z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
            result_df[f'{col}_outlier'] = z_scores > 3
    
    return result_df

def save_data(df: pd.DataFrame, filename: str, format: str = 'csv'):
    """
    Save DataFrame to file
    
    Args:
        df: DataFrame to save
        filename: Output filename
        format: File format ('csv', 'parquet', 'json')
    """
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    if format == 'csv':
        df.to_csv(filename)
    elif format == 'parquet':
        df.to_parquet(filename)
    elif format == 'json':
        df.to_json(filename, orient='index', date_format='iso')
    else:
        raise ValueError(f"Unsupported format: {format}")

def load_data(filename: str, format: str = None) -> pd.DataFrame:
    """
    Load DataFrame from file
    
    Args:
        filename: Input filename
        format: File format (auto-detected if None)
        
    Returns:
        Loaded DataFrame
    """
    if format is None:
        format = filename.split('.')[-1].lower()
    
    if format == 'csv':
        df = pd.read_csv(filename, index_col=0, parse_dates=True)
    elif format == 'parquet':
        df = pd.read_parquet(filename)
    elif format == 'json':
        df = pd.read_json(filename, orient='index')
        df.index = pd.to_datetime(df.index)
    else:
        raise ValueError(f"Unsupported format: {format}")
    
    return df

def merge_data_sources(dfs: List[pd.DataFrame], how: str = 'outer') -> pd.DataFrame:
    """
    Merge multiple data sources
    
    Args:
        dfs: List of DataFrames to merge
        how: How to merge ('outer', 'inner', 'left', 'right')
        
    Returns:
        Merged DataFrame
    """
    if not dfs:
        return pd.DataFrame()
    
    if len(dfs) == 1:
        return dfs[0]
    
    result = dfs[0]
    for df in dfs[1:]:
        result = pd.merge(result, df, left_index=True, right_index=True, how=how, suffixes=('', '_dup'))
        
        # Remove duplicate columns
        result = result.loc[:, ~result.columns.str.endswith('_dup')]
    
    return result

def calculate_correlation_matrix(df: pd.DataFrame, columns: List[str] = None) -> pd.DataFrame:
    """
    Calculate correlation matrix for specified columns
    
    Args:
        df: DataFrame with data
        columns: Columns to include in correlation
        
    Returns:
        Correlation matrix
    """
    if columns is None:
        columns = df.select_dtypes(include=[np.number]).columns.tolist()
    
    available_columns = [col for col in columns if col in df.columns]
    
    if not available_columns:
        return pd.DataFrame()
    
    return df[available_columns].corr()

def get_data_summary(df: pd.DataFrame) -> Dict:
    """
    Get comprehensive data summary
    
    Args:
        df: DataFrame to summarize
        
    Returns:
        Dictionary with summary statistics
    """
    summary = {
        'shape': df.shape,
        'columns': df.columns.tolist(),
        'dtypes': df.dtypes.to_dict(),
        'memory_usage': df.memory_usage(deep=True).sum(),
        'null_counts': df.isnull().sum().to_dict(),
        'date_range': {
            'start': df.index.min() if hasattr(df.index, 'min') else None,
            'end': df.index.max() if hasattr(df.index, 'max') else None
        }
    }
    
    # Numeric columns summary
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    if len(numeric_columns) > 0:
        summary['numeric_summary'] = df[numeric_columns].describe().to_dict()
    
    return summary
