"""
Technical Indicators Module
Implements RSI, MACD, EMA, and other indicators that complement VWAP analysis
"""
import pandas as pd
import numpy as np
from typing import Tuple, Optional

class TechnicalIndicators:
    """
    Collection of technical indicators for trading signal confirmation
    """
    
    @staticmethod
    def rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)
        
        Args:
            prices: Price series (typically close prices)
            period: RSI period (default 14)
            
        Returns:
            RSI values
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def ema(prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Exponential Moving Average (EMA)
        
        Args:
            prices: Price series
            period: EMA period
            
        Returns:
            EMA values
        """
        return prices.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def sma(prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Simple Moving Average (SMA)
        
        Args:
            prices: Price series
            period: SMA period
            
        Returns:
            SMA values
        """
        return prices.rolling(window=period).mean()
    
    @staticmethod
    def macd(prices: pd.Series, 
             fast_period: int = 12, 
             slow_period: int = 26, 
             signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate MACD (Moving Average Convergence Divergence)
        
        Args:
            prices: Price series
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal line EMA period
            
        Returns:
            Tuple of (MACD line, Signal line, Histogram)
        """
        ema_fast = TechnicalIndicators.ema(prices, fast_period)
        ema_slow = TechnicalIndicators.ema(prices, slow_period)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal_period)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(prices: pd.Series, 
                       period: int = 20, 
                       std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate Bollinger Bands
        
        Args:
            prices: Price series
            period: Moving average period
            std_dev: Standard deviation multiplier
            
        Returns:
            Tuple of (Upper band, Middle band, Lower band)
        """
        middle_band = TechnicalIndicators.sma(prices, period)
        std = prices.rolling(window=period).std()
        
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return upper_band, middle_band, lower_band
    
    @staticmethod
    def stochastic(high: pd.Series, 
                  low: pd.Series, 
                  close: pd.Series, 
                  k_period: int = 14, 
                  d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate Stochastic Oscillator
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            k_period: %K period
            d_period: %D period
            
        Returns:
            Tuple of (%K, %D)
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent, d_percent
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Average True Range (ATR)
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            period: ATR period
            
        Returns:
            ATR values
        """
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    @staticmethod
    def volume_profile(prices: pd.Series, volume: pd.Series, bins: int = 20) -> dict:
        """
        Calculate Volume Profile
        
        Args:
            prices: Price series
            volume: Volume series
            bins: Number of price bins
            
        Returns:
            Dictionary with volume profile data
        """
        price_min = prices.min()
        price_max = prices.max()
        
        # Create price bins
        price_bins = np.linspace(price_min, price_max, bins + 1)
        
        # Assign each price to a bin
        price_bin_indices = np.digitize(prices, price_bins) - 1
        price_bin_indices = np.clip(price_bin_indices, 0, bins - 1)
        
        # Calculate volume for each bin
        volume_profile = {}
        for i in range(bins):
            mask = price_bin_indices == i
            bin_volume = volume[mask].sum()
            bin_price_center = (price_bins[i] + price_bins[i + 1]) / 2
            
            volume_profile[bin_price_center] = bin_volume
        
        # Find Point of Control (POC) - price level with highest volume
        poc_price = max(volume_profile, key=volume_profile.get)
        
        return {
            'volume_profile': volume_profile,
            'poc_price': poc_price,
            'total_volume': volume.sum()
        }
    
    @staticmethod
    def money_flow_index(high: pd.Series, 
                        low: pd.Series, 
                        close: pd.Series, 
                        volume: pd.Series, 
                        period: int = 14) -> pd.Series:
        """
        Calculate Money Flow Index (MFI)
        
        Args:
            high: High prices
            low: Low prices
            close: Close prices
            volume: Volume
            period: MFI period
            
        Returns:
            MFI values
        """
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        
        # Positive and negative money flow
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0)
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0)
        
        # Money flow ratio
        positive_mf = positive_flow.rolling(window=period).sum()
        negative_mf = negative_flow.rolling(window=period).sum()
        
        mfr = positive_mf / negative_mf
        mfi = 100 - (100 / (1 + mfr))
        
        return mfi
    
    @staticmethod
    def calculate_all_indicators(df: pd.DataFrame, config: dict = None) -> pd.DataFrame:
        """
        Calculate all technical indicators for a DataFrame
        
        Args:
            df: DataFrame with OHLCV data
            config: Configuration dictionary with indicator parameters
            
        Returns:
            DataFrame with all indicators added
        """
        if config is None:
            config = {
                'rsi_period': 14,
                'ema_fast': 12,
                'ema_slow': 26,
                'macd_signal': 9,
                'bb_period': 20,
                'bb_std': 2.0,
                'stoch_k': 14,
                'stoch_d': 3,
                'atr_period': 14,
                'mfi_period': 14
            }
        
        result_df = df.copy()
        
        # RSI
        result_df['rsi'] = TechnicalIndicators.rsi(df['close'], config['rsi_period'])
        
        # EMAs
        result_df['ema_fast'] = TechnicalIndicators.ema(df['close'], config['ema_fast'])
        result_df['ema_slow'] = TechnicalIndicators.ema(df['close'], config['ema_slow'])
        
        # MACD
        macd_line, signal_line, histogram = TechnicalIndicators.macd(
            df['close'], config['ema_fast'], config['ema_slow'], config['macd_signal']
        )
        result_df['macd'] = macd_line
        result_df['macd_signal'] = signal_line
        result_df['macd_histogram'] = histogram
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(
            df['close'], config['bb_period'], config['bb_std']
        )
        result_df['bb_upper'] = bb_upper
        result_df['bb_middle'] = bb_middle
        result_df['bb_lower'] = bb_lower
        
        # Stochastic
        stoch_k, stoch_d = TechnicalIndicators.stochastic(
            df['high'], df['low'], df['close'], config['stoch_k'], config['stoch_d']
        )
        result_df['stoch_k'] = stoch_k
        result_df['stoch_d'] = stoch_d
        
        # ATR
        result_df['atr'] = TechnicalIndicators.atr(
            df['high'], df['low'], df['close'], config['atr_period']
        )
        
        # MFI
        result_df['mfi'] = TechnicalIndicators.money_flow_index(
            df['high'], df['low'], df['close'], df['volume'], config['mfi_period']
        )
        
        return result_df
