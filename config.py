"""
Configuration settings for the VWAP Trading System
"""
import os
from typing import Dict, Any
from pydantic import BaseModel, Field
from dotenv import load_dotenv

load_dotenv()

class TradingConfig(BaseModel):
    """Trading strategy configuration"""
    # VWAP Settings
    vwap_period: str = "1d"  # Daily VWAP
    std_dev_multiplier_1: float = 1.0
    std_dev_multiplier_2: float = 2.0  # Primary focus on 2nd deviation
    std_dev_multiplier_3: float = 3.0
    
    # Strategy Settings
    enable_continuation_strategy: bool = True
    enable_mean_reversion_strategy: bool = True
    
    # Risk Management
    max_position_size_usd: float = 1000.0
    risk_per_trade_percent: float = 2.0  # 2% of account per trade
    max_leverage: float = 10.0
    stop_loss_percent: float = 2.0
    take_profit_percent: float = 4.0  # 1:2 risk/reward ratio
    
    # Technical Indicators
    rsi_period: int = 14
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0
    
    # Additional confirmation indicators
    ema_fast_period: int = 12
    ema_slow_period: int = 26
    macd_signal_period: int = 9
    
    # Timeframes
    primary_timeframe: str = "1h"  # Primary trading timeframe
    signal_timeframe: str = "15m"  # Signal generation timeframe
    
class BinanceConfig(BaseModel):
    """Binance API configuration"""
    api_key: str = Field(default_factory=lambda: os.getenv("BINANCE_API_KEY", ""))
    api_secret: str = Field(default_factory=lambda: os.getenv("BINANCE_API_SECRET", ""))
    testnet: bool = Field(default_factory=lambda: os.getenv("BINANCE_TESTNET", "true").lower() == "true")
    symbol: str = "BTCUSDT"
    
class SystemConfig(BaseModel):
    """System configuration"""
    paper_trading: bool = True  # Start with paper trading
    log_level: str = "INFO"
    data_storage_path: str = "./data"
    backtest_start_date: str = "2023-01-01"
    backtest_end_date: str = "2024-01-01"
    
    # Real-time trading
    enable_real_time: bool = False
    trade_execution_delay: float = 0.1  # seconds
    
    # Performance tracking
    enable_performance_tracking: bool = True
    save_trades_to_file: bool = True
    
class Config:
    """Main configuration class"""
    def __init__(self):
        self.trading = TradingConfig()
        self.binance = BinanceConfig()
        self.system = SystemConfig()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            "trading": self.trading.model_dump(),
            "binance": self.binance.model_dump(),
            "system": self.system.model_dump()
        }
    
    def validate_config(self) -> bool:
        """Validate configuration settings"""
        if self.system.paper_trading:
            return True
            
        if not self.binance.api_key or not self.binance.api_secret:
            raise ValueError("Binance API credentials required for live trading")
            
        if self.trading.risk_per_trade_percent > 10:
            raise ValueError("Risk per trade cannot exceed 10%")
            
        return True

# Global config instance
config = Config()
