"""
Backtesting Engine
Validates strategy performance on historical data
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from strategies.vwap_strategy import VWAPStrategy, TradingSignal, SignalType
from risk_management.risk_manager import RiskManager, PositionType
from indicators.vwap import VWAPCalculator
from indicators.technical_indicators import TechnicalIndicators

class BacktestEngine:
    """
    Backtesting engine for VWAP trading strategies
    """
    
    def __init__(self, config: Dict):
        """
        Initialize backtest engine
        
        Args:
            config: Backtesting configuration
        """
        self.config = config
        
        # Initialize components
        self.vwap_calculator = VWAPCalculator(
            std_dev_multipliers=[
                config['trading']['std_dev_multiplier_1'],
                config['trading']['std_dev_multiplier_2'],
                config['trading']['std_dev_multiplier_3']
            ]
        )
        
        self.strategy = VWAPStrategy(config['trading'])
        self.risk_manager = RiskManager(config['trading'])
        
        # Backtest results
        self.trades: List[Dict] = []
        self.equity_curve: List[Dict] = []
        self.signals: List[TradingSignal] = []
        
    def prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare data with all indicators for backtesting
        
        Args:
            df: Raw OHLCV data
            
        Returns:
            DataFrame with all indicators
        """
        # Calculate VWAP
        df_with_vwap = self.vwap_calculator.calculate_vwap(df)
        
        # Calculate technical indicators
        indicator_config = {
            'rsi_period': self.config['trading']['rsi_period'],
            'ema_fast': self.config['trading']['ema_fast_period'],
            'ema_slow': self.config['trading']['ema_slow_period'],
            'macd_signal': self.config['trading']['macd_signal_period']
        }
        
        df_with_indicators = TechnicalIndicators.calculate_all_indicators(
            df_with_vwap, indicator_config
        )
        
        return df_with_indicators
    
    def run_backtest(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> Dict:
        """
        Run backtest on historical data
        
        Args:
            df: Historical OHLCV data
            start_date: Start date for backtest
            end_date: End date for backtest
            
        Returns:
            Backtest results dictionary
        """
        print("Starting backtest...")
        
        # Filter data by date range if specified
        if start_date:
            df = df[df.index >= start_date]
        if end_date:
            df = df[df.index <= end_date]
        
        if df.empty:
            return {"error": "No data available for specified date range"}
        
        # Prepare data with indicators
        df_prepared = self.prepare_data(df)
        
        # Reset components
        self.trades = []
        self.equity_curve = []
        self.signals = []
        self.risk_manager = RiskManager(self.config['trading'])
        
        # Track backtest state
        current_position = None
        symbol = "BTCUSDT"
        
        print(f"Backtesting from {df_prepared.index[0]} to {df_prepared.index[-1]}")
        print(f"Total data points: {len(df_prepared)}")
        
        # Main backtest loop
        for i in range(20, len(df_prepared)):  # Start after warm-up period
            current_data = df_prepared.iloc[i]
            current_price = current_data['close']
            current_time = current_data.name
            
            # Update positions with current price
            self.risk_manager.update_positions({symbol: current_price})
            
            # Record equity curve
            portfolio_summary = self.risk_manager.get_portfolio_summary()
            self.equity_curve.append({
                'timestamp': current_time,
                'balance': portfolio_summary['account_balance'],
                'unrealized_pnl': portfolio_summary['unrealized_pnl'],
                'total_value': portfolio_summary['account_balance'] + portfolio_summary['unrealized_pnl']
            })
            
            # Check for new signals if no position
            if symbol not in self.risk_manager.positions:
                signal = self.strategy.get_current_signal(df_prepared.iloc[:i+1])
                
                if signal and signal.confidence > 0.6:
                    self.signals.append(signal)
                    
                    # Calculate position size
                    position_size = self.risk_manager.calculate_position_size(
                        signal.price, signal.stop_loss
                    )
                    
                    if position_size > 0:
                        # Determine position type
                        position_type = PositionType.LONG if signal.signal_type == SignalType.LONG else PositionType.SHORT
                        
                        # Open position
                        success = self.risk_manager.open_position(
                            symbol=symbol,
                            position_type=position_type,
                            entry_price=signal.price,
                            quantity=position_size,
                            stop_loss=signal.stop_loss,
                            take_profit=signal.take_profit
                        )
                        
                        if success:
                            # Record trade entry
                            trade_entry = {
                                'entry_time': current_time,
                                'entry_price': signal.price,
                                'position_type': position_type.value,
                                'quantity': position_size,
                                'stop_loss': signal.stop_loss,
                                'take_profit': signal.take_profit,
                                'strategy_type': signal.strategy_type.value,
                                'confidence': signal.confidence,
                                'reason': signal.reason
                            }
                            
                            current_position = trade_entry
            
            # Check risk limits
            self.risk_manager.check_risk_limits()
            
            # If position was closed, record the trade
            if current_position and symbol not in self.risk_manager.positions:
                # Position was closed, complete the trade record
                if self.trades and 'exit_time' not in self.trades[-1]:
                    # Find the last incomplete trade and complete it
                    last_trade = self.trades[-1]
                    last_trade.update({
                        'exit_time': current_time,
                        'exit_price': current_price,
                        'pnl': self.risk_manager.total_pnl - sum(t.get('pnl', 0) for t in self.trades[:-1])
                    })
                else:
                    # Add new completed trade
                    current_position.update({
                        'exit_time': current_time,
                        'exit_price': current_price,
                        'pnl': 0  # Will be calculated properly
                    })
                    self.trades.append(current_position)
                
                current_position = None
        
        # Calculate final results
        results = self._calculate_results(df_prepared)
        
        print(f"Backtest completed. Total trades: {len(self.trades)}")
        print(f"Final balance: ${results['final_balance']:.2f}")
        print(f"Total return: {results['total_return']:.2f}%")
        print(f"Win rate: {results['win_rate']:.2f}%")
        
        return results
    
    def _calculate_results(self, df: pd.DataFrame) -> Dict:
        """
        Calculate backtest performance metrics
        
        Args:
            df: DataFrame with backtest data
            
        Returns:
            Dictionary with performance metrics
        """
        portfolio_summary = self.risk_manager.get_portfolio_summary()
        
        # Basic metrics
        initial_balance = self.config['trading']['initial_balance']
        final_balance = portfolio_summary['account_balance']
        total_return = ((final_balance - initial_balance) / initial_balance) * 100
        
        # Trade analysis
        winning_trades = [t for t in self.trades if t.get('pnl', 0) > 0]
        losing_trades = [t for t in self.trades if t.get('pnl', 0) < 0]
        
        win_rate = (len(winning_trades) / len(self.trades) * 100) if self.trades else 0
        
        avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0
        
        profit_factor = abs(sum(t['pnl'] for t in winning_trades) / sum(t['pnl'] for t in losing_trades)) if losing_trades else float('inf')
        
        # Risk metrics
        equity_values = [point['total_value'] for point in self.equity_curve]
        if equity_values:
            peak = np.maximum.accumulate(equity_values)
            drawdown = (peak - equity_values) / peak * 100
            max_drawdown = np.max(drawdown)
        else:
            max_drawdown = 0
        
        # Sharpe ratio (simplified)
        if self.equity_curve:
            returns = pd.Series(equity_values).pct_change().dropna()
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        else:
            sharpe_ratio = 0
        
        return {
            'initial_balance': initial_balance,
            'final_balance': final_balance,
            'total_return': total_return,
            'total_trades': len(self.trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': self.trades,
            'equity_curve': self.equity_curve,
            'signals': self.signals
        }
    
    def plot_results(self, df: pd.DataFrame, results: Dict) -> go.Figure:
        """
        Create interactive plot of backtest results
        
        Args:
            df: Historical data
            results: Backtest results
            
        Returns:
            Plotly figure
        """
        # Create subplots
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('Price & VWAP', 'Portfolio Value', 'Drawdown'),
            vertical_spacing=0.08,
            row_heights=[0.5, 0.3, 0.2]
        )
        
        # Price and VWAP plot
        fig.add_trace(
            go.Candlestick(
                x=df.index,
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='Price'
            ),
            row=1, col=1
        )
        
        # VWAP and bands
        fig.add_trace(
            go.Scatter(x=df.index, y=df['vwap'], name='VWAP', line=dict(color='blue')),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(x=df.index, y=df['vwap_upper_2'], name='Upper Band 2σ', 
                      line=dict(color='red', dash='dash')),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(x=df.index, y=df['vwap_lower_2'], name='Lower Band 2σ', 
                      line=dict(color='green', dash='dash')),
            row=1, col=1
        )
        
        # Trade markers
        for trade in results['trades']:
            if 'exit_time' in trade:
                # Entry marker
                fig.add_trace(
                    go.Scatter(
                        x=[trade['entry_time']],
                        y=[trade['entry_price']],
                        mode='markers',
                        marker=dict(
                            symbol='triangle-up' if trade['position_type'] == 'LONG' else 'triangle-down',
                            size=10,
                            color='green' if trade['position_type'] == 'LONG' else 'red'
                        ),
                        name=f"{trade['position_type']} Entry",
                        showlegend=False
                    ),
                    row=1, col=1
                )
                
                # Exit marker
                fig.add_trace(
                    go.Scatter(
                        x=[trade['exit_time']],
                        y=[trade['exit_price']],
                        mode='markers',
                        marker=dict(
                            symbol='x',
                            size=8,
                            color='blue'
                        ),
                        name='Exit',
                        showlegend=False
                    ),
                    row=1, col=1
                )
        
        # Portfolio value
        if results['equity_curve']:
            equity_df = pd.DataFrame(results['equity_curve'])
            fig.add_trace(
                go.Scatter(
                    x=equity_df['timestamp'],
                    y=equity_df['total_value'],
                    name='Portfolio Value',
                    line=dict(color='purple')
                ),
                row=2, col=1
            )
        
        # Drawdown
        if results['equity_curve']:
            equity_values = [point['total_value'] for point in results['equity_curve']]
            peak = np.maximum.accumulate(equity_values)
            drawdown = (peak - equity_values) / peak * 100
            
            fig.add_trace(
                go.Scatter(
                    x=equity_df['timestamp'],
                    y=-drawdown,  # Negative for visual clarity
                    name='Drawdown %',
                    fill='tonexty',
                    line=dict(color='red')
                ),
                row=3, col=1
            )
        
        # Update layout
        fig.update_layout(
            title=f'VWAP Strategy Backtest Results - Return: {results["total_return"]:.2f}%',
            xaxis_rangeslider_visible=False,
            height=800
        )
        
        return fig
