"""
Test script to verify the VWAP trading system components
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicators.vwap import VWAPCalculator
from indicators.technical_indicators import TechnicalIndicators
from strategies.vwap_strategy import VWAPStrategy
from risk_management.risk_manager import RiskManager
from config import config

def generate_sample_data(days: int = 30) -> pd.DataFrame:
    """Generate sample OHLCV data for testing"""
    
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    date_range = pd.date_range(start=start_date, end=end_date, freq='1H')
    
    # Generate realistic price data
    np.random.seed(42)  # For reproducible results
    
    # Start with a base price
    base_price = 45000.0
    
    # Generate price movements
    returns = np.random.normal(0, 0.02, len(date_range))  # 2% hourly volatility
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1000))  # Minimum price of $1000
    
    # Create OHLCV data
    data = []
    for i, (timestamp, price) in enumerate(zip(date_range, prices)):
        # Generate realistic OHLC from close price
        volatility = np.random.uniform(0.005, 0.02)  # 0.5% to 2% intrabar volatility
        
        high = price * (1 + volatility * np.random.uniform(0.3, 1.0))
        low = price * (1 - volatility * np.random.uniform(0.3, 1.0))
        
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1]
        
        close_price = price
        
        # Ensure OHLC relationships
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        # Generate volume (higher volume during price movements)
        price_change = abs(close_price - open_price) / open_price
        base_volume = np.random.uniform(100, 1000)
        volume = base_volume * (1 + price_change * 10)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=date_range)
    return df

def test_vwap_calculator():
    """Test VWAP calculator"""
    print("Testing VWAP Calculator...")
    
    # Generate sample data
    df = generate_sample_data(10)
    
    # Initialize VWAP calculator
    vwap_calc = VWAPCalculator()
    
    # Calculate VWAP
    df_with_vwap = vwap_calc.calculate_vwap(df)
    
    # Check results
    assert 'vwap' in df_with_vwap.columns, "VWAP column missing"
    assert 'vwap_upper_1' in df_with_vwap.columns, "Upper band 1 missing"
    assert 'vwap_lower_1' in df_with_vwap.columns, "Lower band 1 missing"
    assert 'vwap_upper_2' in df_with_vwap.columns, "Upper band 2 missing"
    assert 'vwap_lower_2' in df_with_vwap.columns, "Lower band 2 missing"
    
    # Check that VWAP values are reasonable
    vwap_values = df_with_vwap['vwap'].dropna()
    assert len(vwap_values) > 0, "No VWAP values calculated"
    
    # Check that bands are properly ordered
    for i in range(len(df_with_vwap)):
        row = df_with_vwap.iloc[i]
        if not pd.isna(row['vwap']):
            assert row['vwap_upper_1'] >= row['vwap'], f"Upper band 1 below VWAP at index {i}"
            assert row['vwap_lower_1'] <= row['vwap'], f"Lower band 1 above VWAP at index {i}"
            assert row['vwap_upper_2'] >= row['vwap_upper_1'], f"Upper band 2 below upper band 1 at index {i}"
            assert row['vwap_lower_2'] <= row['vwap_lower_1'], f"Lower band 2 above lower band 1 at index {i}"
    
    print("✓ VWAP Calculator test passed")
    return df_with_vwap

def test_technical_indicators():
    """Test technical indicators"""
    print("Testing Technical Indicators...")
    
    # Generate sample data
    df = generate_sample_data(30)
    
    # Calculate indicators
    indicator_config = {
        'rsi_period': 14,
        'ema_fast': 12,
        'ema_slow': 26,
        'macd_signal': 9,
        'bb_period': 20,
        'bb_std': 2.0,
        'stoch_k': 14,
        'stoch_d': 3,
        'atr_period': 14,
        'mfi_period': 14
    }
    df_with_indicators = TechnicalIndicators.calculate_all_indicators(df, indicator_config)
    
    # Check that indicators were added
    expected_indicators = ['rsi', 'ema_fast', 'ema_slow', 'macd', 'macd_signal', 'bb_upper', 'bb_lower']
    for indicator in expected_indicators:
        assert indicator in df_with_indicators.columns, f"Missing indicator: {indicator}"
    
    # Check RSI bounds
    rsi_values = df_with_indicators['rsi'].dropna()
    assert all(0 <= rsi <= 100 for rsi in rsi_values), "RSI values out of bounds"
    
    print("✓ Technical Indicators test passed")
    return df_with_indicators

def test_strategy():
    """Test trading strategy"""
    print("Testing Trading Strategy...")
    
    # Generate sample data with indicators
    df = generate_sample_data(30)
    
    # Add VWAP
    vwap_calc = VWAPCalculator()
    df_with_vwap = vwap_calc.calculate_vwap(df)
    
    # Add technical indicators
    indicator_config = {
        'rsi_period': 14,
        'ema_fast': 12,
        'ema_slow': 26,
        'macd_signal': 9,
        'bb_period': 20,
        'bb_std': 2.0,
        'stoch_k': 14,
        'stoch_d': 3,
        'atr_period': 14,
        'mfi_period': 14
    }
    df_with_indicators = TechnicalIndicators.calculate_all_indicators(df_with_vwap, indicator_config)
    
    # Initialize strategy
    strategy = VWAPStrategy(config.trading.model_dump())
    
    # Generate signals
    signals = strategy.generate_signals(df_with_indicators)
    
    print(f"Generated {len(signals)} signals")
    
    # Check signal structure
    if signals:
        signal = signals[0]
        assert hasattr(signal, 'signal_type'), "Signal missing signal_type"
        assert hasattr(signal, 'strategy_type'), "Signal missing strategy_type"
        assert hasattr(signal, 'price'), "Signal missing price"
        assert hasattr(signal, 'confidence'), "Signal missing confidence"
        assert 0 <= signal.confidence <= 1, "Signal confidence out of bounds"
    
    print("✓ Trading Strategy test passed")
    return signals

def test_risk_manager():
    """Test risk manager"""
    print("Testing Risk Manager...")
    
    # Initialize risk manager
    risk_config = config.trading.model_dump()
    risk_manager = RiskManager(risk_config)
    
    # Test position sizing
    entry_price = 45000.0
    stop_loss = 44000.0
    position_size = risk_manager.calculate_position_size(entry_price, stop_loss)
    
    assert position_size > 0, "Position size should be positive"
    
    # Test position opening
    from risk_management.risk_manager import PositionType
    success = risk_manager.open_position(
        symbol="BTCUSDT",
        position_type=PositionType.LONG,
        entry_price=entry_price,
        quantity=position_size,
        stop_loss=stop_loss,
        take_profit=46000.0
    )
    
    assert success, "Position should open successfully"
    assert "BTCUSDT" in risk_manager.positions, "Position should be tracked"
    
    # Test position update
    risk_manager.update_positions({"BTCUSDT": 45500.0})
    
    # Test portfolio summary
    summary = risk_manager.get_portfolio_summary()
    assert 'account_balance' in summary, "Portfolio summary missing account_balance"
    assert 'open_positions' in summary, "Portfolio summary missing open_positions"
    
    print("✓ Risk Manager test passed")

def test_integration():
    """Test system integration"""
    print("Testing System Integration...")
    
    # Generate sample data
    df = generate_sample_data(30)
    
    # Process through full pipeline
    vwap_calc = VWAPCalculator()
    df_with_vwap = vwap_calc.calculate_vwap(df)
    
    indicator_config = {
        'rsi_period': 14,
        'ema_fast': 12,
        'ema_slow': 26,
        'macd_signal': 9,
        'bb_period': 20,
        'bb_std': 2.0,
        'stoch_k': 14,
        'stoch_d': 3,
        'atr_period': 14,
        'mfi_period': 14
    }
    df_with_indicators = TechnicalIndicators.calculate_all_indicators(df_with_vwap, indicator_config)
    
    strategy = VWAPStrategy(config.trading.model_dump())
    risk_manager = RiskManager(config.trading.model_dump())
    
    # Simulate trading
    trades_executed = 0
    
    for i in range(20, len(df_with_indicators)):
        current_data = df_with_indicators.iloc[:i+1]
        signal = strategy.get_current_signal(current_data)
        
        if signal and signal.confidence > 0.6:
            # Calculate position size
            position_size = risk_manager.calculate_position_size(signal.price, signal.stop_loss)
            
            if position_size > 0:
                from strategies.vwap_strategy import SignalType
                from risk_management.risk_manager import PositionType
                
                position_type = PositionType.LONG if signal.signal_type == SignalType.LONG else PositionType.SHORT
                
                # Check if we can open position
                can_open, _ = risk_manager.can_open_position("BTCUSDT", position_size * signal.price)
                
                if can_open and "BTCUSDT" not in risk_manager.positions:
                    success = risk_manager.open_position(
                        symbol="BTCUSDT",
                        position_type=position_type,
                        entry_price=signal.price,
                        quantity=position_size,
                        stop_loss=signal.stop_loss,
                        take_profit=signal.take_profit
                    )
                    
                    if success:
                        trades_executed += 1
        
        # Update positions
        current_price = df_with_indicators.iloc[i]['close']
        risk_manager.update_positions({"BTCUSDT": current_price})
    
    print(f"Executed {trades_executed} trades in simulation")
    
    # Get final summary
    final_summary = risk_manager.get_portfolio_summary()
    print(f"Final balance: ${final_summary['account_balance']:,.2f}")
    print(f"Total PnL: ${final_summary['total_pnl']:,.2f}")
    print(f"Win rate: {final_summary['win_rate']:.1f}%")
    
    print("✓ System Integration test passed")

def main():
    """Run all tests"""
    print("Starting VWAP Trading System Tests")
    print("=" * 50)
    
    try:
        # Test individual components
        test_vwap_calculator()
        test_technical_indicators()
        test_strategy()
        test_risk_manager()
        
        # Test integration
        test_integration()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed successfully!")
        print("The VWAP trading system is ready to use.")
        print("\nNext steps:")
        print("1. Set up your .env file with Binance API credentials (optional)")
        print("2. Run a backtest: python main.py backtest --plot")
        print("3. Try paper trading: python main.py live")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
