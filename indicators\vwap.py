"""
VWAP (Volume Weighted Average Price) Implementation
Based on the TradingView Pine Script implementation with standard deviation bands
"""
import pandas as pd
import numpy as np
from typing import Tuple, Optional
from datetime import datetime, time

class VWAPCalculator:
    """
    VWAP Calculator with standard deviation bands
    Implements daily VWAP reset and configurable standard deviation multipliers
    """
    
    def __init__(self, 
                 std_dev_multipliers: list = [1.0, 2.0, 3.0],
                 anchor_period: str = "1D",
                 source: str = "hlc3"):
        """
        Initialize VWAP calculator
        
        Args:
            std_dev_multipliers: List of standard deviation multipliers for bands
            anchor_period: Period for VWAP reset ('1D' for daily, '1W' for weekly, etc.)
            source: Price source ('hlc3', 'close', 'ohlc4')
        """
        self.std_dev_multipliers = std_dev_multipliers
        self.anchor_period = anchor_period
        self.source = source
        
    def _get_source_price(self, df: pd.DataFrame) -> pd.Series:
        """Get source price based on configuration"""
        if self.source == "hlc3":
            return (df['high'] + df['low'] + df['close']) / 3
        elif self.source == "ohlc4":
            return (df['open'] + df['high'] + df['low'] + df['close']) / 4
        elif self.source == "close":
            return df['close']
        else:
            raise ValueError(f"Unsupported source: {self.source}")
    
    def _identify_new_periods(self, df: pd.DataFrame) -> pd.Series:
        """Identify new VWAP periods based on anchor period"""
        if self.anchor_period == "1D":
            # Reset VWAP at the start of each day
            return df.index.to_series().dt.date != df.index.to_series().dt.date.shift(1)
        elif self.anchor_period == "1W":
            # Reset VWAP at the start of each week
            return df.index.to_series().dt.isocalendar().week != df.index.to_series().dt.isocalendar().week.shift(1)
        elif self.anchor_period == "1M":
            # Reset VWAP at the start of each month
            return df.index.to_series().dt.month != df.index.to_series().dt.month.shift(1)
        else:
            # For session-based, reset at market open (assuming 24/7 crypto market, use daily)
            return df.index.to_series().dt.date != df.index.to_series().dt.date.shift(1)
    
    def calculate_vwap(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate VWAP with standard deviation bands
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with VWAP and band columns
        """
        if df.empty:
            return df
            
        # Ensure we have required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
        
        # Get source price
        source_price = self._get_source_price(df)
        
        # Identify new periods
        new_period = self._identify_new_periods(df)
        
        # Calculate VWAP and standard deviation
        result_df = df.copy()
        
        # Initialize arrays
        vwap_values = np.full(len(df), np.nan)
        std_dev_values = np.full(len(df), np.nan)
        
        # Calculate cumulative values for each VWAP period
        cumulative_pv = 0.0  # Price * Volume
        cumulative_volume = 0.0
        cumulative_pv_squared = 0.0  # (Price * Volume)^2 for variance calculation
        
        for i in range(len(df)):
            # Reset on new period
            if new_period.iloc[i] or i == 0:
                cumulative_pv = 0.0
                cumulative_volume = 0.0
                cumulative_pv_squared = 0.0
            
            # Current values
            price = source_price.iloc[i]
            volume = df['volume'].iloc[i]
            
            if volume > 0 and not np.isnan(price):
                # Update cumulative values
                pv = price * volume
                cumulative_pv += pv
                cumulative_volume += volume
                cumulative_pv_squared += pv * price  # This gives us sum(price^2 * volume)
                
                # Calculate VWAP
                if cumulative_volume > 0:
                    vwap = cumulative_pv / cumulative_volume
                    vwap_values[i] = vwap
                    
                    # Calculate standard deviation
                    # Variance = E[X^2] - E[X]^2 where X is price weighted by volume
                    mean_price_squared = cumulative_pv_squared / cumulative_volume
                    variance = mean_price_squared - (vwap ** 2)
                    
                    if variance > 0:
                        std_dev_values[i] = np.sqrt(variance)
        
        # Add VWAP to result
        result_df['vwap'] = vwap_values
        result_df['vwap_std'] = std_dev_values
        
        # Calculate bands
        for i, multiplier in enumerate(self.std_dev_multipliers, 1):
            upper_band = vwap_values + (std_dev_values * multiplier)
            lower_band = vwap_values - (std_dev_values * multiplier)
            
            result_df[f'vwap_upper_{i}'] = upper_band
            result_df[f'vwap_lower_{i}'] = lower_band
        
        return result_df
    
    def get_current_vwap_position(self, df: pd.DataFrame, current_price: float) -> dict:
        """
        Get current price position relative to VWAP bands
        
        Args:
            df: DataFrame with VWAP calculations
            current_price: Current market price
            
        Returns:
            Dictionary with position information
        """
        if df.empty:
            return {}
            
        latest = df.iloc[-1]
        vwap = latest['vwap']
        
        if np.isnan(vwap):
            return {}
        
        # Calculate position relative to VWAP
        vwap_distance = (current_price - vwap) / vwap * 100  # Percentage distance
        
        # Determine which band the price is in
        position_info = {
            'vwap': vwap,
            'current_price': current_price,
            'distance_from_vwap_pct': vwap_distance,
            'above_vwap': current_price > vwap
        }
        
        # Check band positions
        for i, multiplier in enumerate(self.std_dev_multipliers, 1):
            upper_band = latest[f'vwap_upper_{i}']
            lower_band = latest[f'vwap_lower_{i}']
            
            position_info[f'above_upper_band_{i}'] = current_price > upper_band
            position_info[f'below_lower_band_{i}'] = current_price < lower_band
            position_info[f'within_band_{i}'] = lower_band <= current_price <= upper_band
            
            if not np.isnan(upper_band) and not np.isnan(lower_band):
                band_width = upper_band - lower_band
                if band_width > 0:
                    position_in_band = (current_price - lower_band) / band_width
                    position_info[f'position_in_band_{i}'] = position_in_band
        
        return position_info
