"""
VWAP Trading Strategy Implementation
Implements both continuation and mean reversion strategies based on VWAP levels
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

class SignalType(Enum):
    """Trading signal types"""
    LONG = "LONG"
    SHORT = "SHORT"
    CLOSE_LONG = "CLOSE_LONG"
    CLOSE_SHORT = "CLOSE_SHORT"
    NO_SIGNAL = "NO_SIGNAL"

class StrategyType(Enum):
    """Strategy types"""
    CONTINUATION = "CONTINUATION"
    MEAN_REVERSION = "MEAN_REVERSION"

@dataclass
class TradingSignal:
    """Trading signal data structure"""
    timestamp: datetime
    signal_type: SignalType
    strategy_type: StrategyType
    price: float
    confidence: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    reason: str = ""
    indicators: Dict = None

class VWAPStrategy:
    """
    VWAP-based trading strategy with continuation and mean reversion approaches
    """
    
    def __init__(self, config: Dict):
        """
        Initialize VWAP strategy
        
        Args:
            config: Strategy configuration dictionary
        """
        self.config = config
        
        # Strategy settings
        self.enable_continuation = config.get('enable_continuation_strategy', True)
        self.enable_mean_reversion = config.get('enable_mean_reversion_strategy', True)
        
        # VWAP settings
        self.std_dev_multiplier_1 = config.get('std_dev_multiplier_1', 1.0)
        self.std_dev_multiplier_2 = config.get('std_dev_multiplier_2', 2.0)
        self.std_dev_multiplier_3 = config.get('std_dev_multiplier_3', 3.0)
        
        # Risk management
        self.stop_loss_percent = config.get('stop_loss_percent', 2.0)
        self.take_profit_percent = config.get('take_profit_percent', 4.0)
        
        # Technical indicator thresholds
        self.rsi_oversold = config.get('rsi_oversold', 30.0)
        self.rsi_overbought = config.get('rsi_overbought', 70.0)
        
        # Signal history for tracking
        self.signal_history: List[TradingSignal] = []
        self.current_position = None
        
    def analyze_vwap_position(self, df: pd.DataFrame, current_idx: int) -> Dict:
        """
        Analyze current price position relative to VWAP bands
        
        Args:
            df: DataFrame with VWAP and indicator data
            current_idx: Current data index
            
        Returns:
            Dictionary with position analysis
        """
        if current_idx < 0 or current_idx >= len(df):
            return {}
            
        current_data = df.iloc[current_idx]
        current_price = current_data['close']
        vwap = current_data['vwap']
        
        if pd.isna(vwap):
            return {}
        
        # Calculate position relative to VWAP
        vwap_distance_pct = (current_price - vwap) / vwap * 100
        
        # Band positions
        upper_1 = current_data['vwap_upper_1']
        lower_1 = current_data['vwap_lower_1']
        upper_2 = current_data['vwap_upper_2']
        lower_2 = current_data['vwap_lower_2']
        
        analysis = {
            'price': current_price,
            'vwap': vwap,
            'vwap_distance_pct': vwap_distance_pct,
            'above_vwap': current_price > vwap,
            'at_upper_1': current_price >= upper_1,
            'at_lower_1': current_price <= lower_1,
            'at_upper_2': current_price >= upper_2,
            'at_lower_2': current_price <= lower_2,
            'within_bands_1': lower_1 <= current_price <= upper_1,
            'within_bands_2': lower_2 <= current_price <= upper_2
        }
        
        # Calculate band penetration percentage
        if not pd.isna(upper_2) and not pd.isna(lower_2):
            band_width = upper_2 - lower_2
            if band_width > 0:
                position_in_band = (current_price - lower_2) / band_width
                analysis['position_in_band_2'] = position_in_band
        
        return analysis
    
    def check_continuation_signals(self, df: pd.DataFrame, current_idx: int) -> Optional[TradingSignal]:
        """
        Check for continuation trading signals
        Price moves away from VWAP and approaches 2nd standard deviation
        
        Args:
            df: DataFrame with market data and indicators
            current_idx: Current data index
            
        Returns:
            Trading signal if found, None otherwise
        """
        if not self.enable_continuation or current_idx < 20:
            return None
            
        current_data = df.iloc[current_idx]
        prev_data = df.iloc[current_idx - 1]
        
        vwap_analysis = self.analyze_vwap_position(df, current_idx)
        if not vwap_analysis:
            return None
        
        current_price = current_data['close']
        rsi = current_data['rsi']
        macd = current_data['macd']
        macd_signal = current_data['macd_signal']
        
        # Look for momentum continuation signals
        
        # Long continuation signal
        if (vwap_analysis['above_vwap'] and 
            vwap_analysis['vwap_distance_pct'] > 0.5 and  # Price moving away from VWAP
            current_price > prev_data['close'] and  # Price momentum up
            not vwap_analysis['at_upper_2'] and  # Not yet at extreme
            rsi < 70 and  # Not overbought
            macd > macd_signal):  # MACD bullish
            
            confidence = self._calculate_confidence(df, current_idx, 'continuation_long')
            
            if confidence > 0.6:
                stop_loss = current_price * (1 - self.stop_loss_percent / 100)
                take_profit = current_price * (1 + self.take_profit_percent / 100)
                
                return TradingSignal(
                    timestamp=current_data.name,
                    signal_type=SignalType.LONG,
                    strategy_type=StrategyType.CONTINUATION,
                    price=current_price,
                    confidence=confidence,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reason="Continuation long: Price moving away from VWAP with momentum",
                    indicators={'rsi': rsi, 'macd': macd, 'vwap_distance': vwap_analysis['vwap_distance_pct']}
                )
        
        # Short continuation signal
        elif (not vwap_analysis['above_vwap'] and 
              vwap_analysis['vwap_distance_pct'] < -0.5 and  # Price moving away from VWAP
              current_price < prev_data['close'] and  # Price momentum down
              not vwap_analysis['at_lower_2'] and  # Not yet at extreme
              rsi > 30 and  # Not oversold
              macd < macd_signal):  # MACD bearish
            
            confidence = self._calculate_confidence(df, current_idx, 'continuation_short')
            
            if confidence > 0.6:
                stop_loss = current_price * (1 + self.stop_loss_percent / 100)
                take_profit = current_price * (1 - self.take_profit_percent / 100)
                
                return TradingSignal(
                    timestamp=current_data.name,
                    signal_type=SignalType.SHORT,
                    strategy_type=StrategyType.CONTINUATION,
                    price=current_price,
                    confidence=confidence,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reason="Continuation short: Price moving away from VWAP with momentum",
                    indicators={'rsi': rsi, 'macd': macd, 'vwap_distance': vwap_analysis['vwap_distance_pct']}
                )
        
        return None
    
    def check_mean_reversion_signals(self, df: pd.DataFrame, current_idx: int) -> Optional[TradingSignal]:
        """
        Check for mean reversion trading signals
        Price at extremes, expecting reversion back to VWAP
        
        Args:
            df: DataFrame with market data and indicators
            current_idx: Current data index
            
        Returns:
            Trading signal if found, None otherwise
        """
        if not self.enable_mean_reversion or current_idx < 20:
            return None
            
        current_data = df.iloc[current_idx]
        
        vwap_analysis = self.analyze_vwap_position(df, current_idx)
        if not vwap_analysis:
            return None
        
        current_price = current_data['close']
        rsi = current_data['rsi']
        stoch_k = current_data['stoch_k']
        
        # Long mean reversion signal (price at lower extreme)
        if (vwap_analysis['at_lower_2'] and  # Price at or below 2nd lower band
            rsi < self.rsi_oversold and  # RSI oversold
            stoch_k < 20):  # Stochastic oversold
            
            confidence = self._calculate_confidence(df, current_idx, 'mean_reversion_long')
            
            if confidence > 0.7:
                stop_loss = current_price * (1 - self.stop_loss_percent / 100)
                take_profit = vwap_analysis['vwap']  # Target VWAP for mean reversion
                
                return TradingSignal(
                    timestamp=current_data.name,
                    signal_type=SignalType.LONG,
                    strategy_type=StrategyType.MEAN_REVERSION,
                    price=current_price,
                    confidence=confidence,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reason="Mean reversion long: Price at extreme low, expecting reversion to VWAP",
                    indicators={'rsi': rsi, 'stoch_k': stoch_k, 'vwap_distance': vwap_analysis['vwap_distance_pct']}
                )
        
        # Short mean reversion signal (price at upper extreme)
        elif (vwap_analysis['at_upper_2'] and  # Price at or above 2nd upper band
              rsi > self.rsi_overbought and  # RSI overbought
              stoch_k > 80):  # Stochastic overbought
            
            confidence = self._calculate_confidence(df, current_idx, 'mean_reversion_short')
            
            if confidence > 0.7:
                stop_loss = current_price * (1 + self.stop_loss_percent / 100)
                take_profit = vwap_analysis['vwap']  # Target VWAP for mean reversion
                
                return TradingSignal(
                    timestamp=current_data.name,
                    signal_type=SignalType.SHORT,
                    strategy_type=StrategyType.MEAN_REVERSION,
                    price=current_price,
                    confidence=confidence,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reason="Mean reversion short: Price at extreme high, expecting reversion to VWAP",
                    indicators={'rsi': rsi, 'stoch_k': stoch_k, 'vwap_distance': vwap_analysis['vwap_distance_pct']}
                )
        
        return None
    
    def _calculate_confidence(self, df: pd.DataFrame, current_idx: int, signal_type: str) -> float:
        """
        Calculate confidence score for a trading signal
        
        Args:
            df: DataFrame with market data
            current_idx: Current data index
            signal_type: Type of signal being evaluated
            
        Returns:
            Confidence score between 0 and 1
        """
        confidence = 0.5  # Base confidence
        
        current_data = df.iloc[current_idx]
        
        # Volume confirmation
        avg_volume = df['volume'].rolling(20).mean().iloc[current_idx]
        if current_data['volume'] > avg_volume * 1.2:
            confidence += 0.1
        
        # ATR for volatility assessment
        atr = current_data['atr']
        avg_atr = df['atr'].rolling(20).mean().iloc[current_idx]
        if atr > avg_atr * 1.1:  # Higher volatility can be good for breakouts
            confidence += 0.05
        
        # MACD confirmation
        macd = current_data['macd']
        macd_signal = current_data['macd_signal']
        
        if 'long' in signal_type and macd > macd_signal:
            confidence += 0.1
        elif 'short' in signal_type and macd < macd_signal:
            confidence += 0.1
        
        # Time-based factors (avoid trading during low liquidity periods)
        hour = current_data.name.hour if hasattr(current_data.name, 'hour') else 12
        if 8 <= hour <= 20:  # Active trading hours
            confidence += 0.05
        
        return min(confidence, 1.0)
    
    def generate_signals(self, df: pd.DataFrame) -> List[TradingSignal]:
        """
        Generate trading signals for the entire DataFrame
        
        Args:
            df: DataFrame with market data and indicators
            
        Returns:
            List of trading signals
        """
        signals = []
        
        for i in range(20, len(df)):  # Start after warm-up period
            # Check for continuation signals
            continuation_signal = self.check_continuation_signals(df, i)
            if continuation_signal:
                signals.append(continuation_signal)
                continue
            
            # Check for mean reversion signals
            mean_reversion_signal = self.check_mean_reversion_signals(df, i)
            if mean_reversion_signal:
                signals.append(mean_reversion_signal)
        
        self.signal_history.extend(signals)
        return signals
    
    def get_current_signal(self, df: pd.DataFrame) -> Optional[TradingSignal]:
        """
        Get trading signal for the most recent data point
        
        Args:
            df: DataFrame with market data and indicators
            
        Returns:
            Trading signal if found, None otherwise
        """
        if len(df) < 21:
            return None
        
        current_idx = len(df) - 1
        
        # Check continuation first
        signal = self.check_continuation_signals(df, current_idx)
        if signal:
            return signal
        
        # Then check mean reversion
        signal = self.check_mean_reversion_signals(df, current_idx)
        if signal:
            return signal
        
        return None
