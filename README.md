# VWAP Trading System

A comprehensive Python-based automated trading system for Bitcoin USDT perpetual futures on Binance using VWAP (Volume Weighted Average Price) as the primary indicator.

## Features

### Core Trading Strategy
- **Daily VWAP with 2nd standard deviation bands** as primary signal generator
- **Two trading strategies**:
  1. **Continuation trades**: Enter positions when price moves away from VWAP and approaches the 2nd standard deviation
  2. **Mean reversion trades**: Enter positions when price is at extremes, expecting reversion back to VWAP

### Technical Requirements
- Binance public API integration for real-time and historical Bitcoin USDT perpetual futures data
- VWAP calculation with configurable standard deviation bands
- Additional technical indicators for confirmation (RSI, MACD, EMA, Bollinger Bands, Stochastic, ATR, MFI)
- Support for both long and short position entries
- Comprehensive risk management with configurable risk/reward ratios

### System Features
- **Backtesting capability** on historical data to validate strategy performance
- **Real-time signal generation** and trade execution
- **Position sizing** based on risk parameters
- **Stop-loss and take-profit** mechanisms
- **Performance analytics** and reporting
- **Paper trading mode** for testing before live execution

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd vwap-trading-system
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your Binance API credentials (optional for backtesting)
```

## Quick Start

### 1. Run a Backtest

```bash
python main.py backtest --start-date 2023-01-01 --end-date 2024-01-01 --plot
```

This will:
- Download historical data for the specified period
- Run the VWAP strategy backtest
- Generate performance reports and plots
- Save results to JSON files

### 2. Paper Trading

```bash
python main.py live
```

This will:
- Start paper trading mode (no real money)
- Load historical data for strategy initialization
- Begin real-time signal generation
- Execute trades in simulation

### 3. Download Historical Data

```bash
python main.py data --download --days 90
```

## Configuration

The system is highly configurable through `config.py`. Key settings include:

### Trading Configuration
```python
# VWAP Settings
vwap_period: str = "1d"  # Daily VWAP
std_dev_multiplier_2: float = 2.0  # Primary focus on 2nd deviation

# Strategy Settings
enable_continuation_strategy: bool = True
enable_mean_reversion_strategy: bool = True

# Risk Management
risk_per_trade_percent: float = 2.0  # 2% of account per trade
stop_loss_percent: float = 2.0
take_profit_percent: float = 4.0  # 1:2 risk/reward ratio
```

### Technical Indicators
```python
rsi_period: int = 14
rsi_oversold: float = 30.0
rsi_overbought: float = 70.0
ema_fast_period: int = 12
ema_slow_period: int = 26
```

## System Architecture

The system is built with a modular architecture:

```
├── config.py                 # Configuration management
├── main.py                   # Main application entry point
├── data/
│   └── binance_client.py     # Binance API integration
├── indicators/
│   ├── vwap.py              # VWAP calculation
│   └── technical_indicators.py # RSI, MACD, etc.
├── strategies/
│   └── vwap_strategy.py     # Trading strategy logic
├── risk_management/
│   └── risk_manager.py      # Position sizing and risk controls
├── backtesting/
│   └── backtest_engine.py   # Backtesting framework
├── execution/
│   └── trade_executor.py    # Real-time trading execution
├── analytics/
│   └── performance_analyzer.py # Performance metrics and reporting
└── utils/
    └── data_utils.py        # Data utility functions
```

## Strategy Details

### VWAP Calculation
- Uses daily VWAP reset (configurable anchor period)
- Calculates standard deviation bands at 1σ, 2σ, and 3σ levels
- Primary focus on 2nd standard deviation for signal generation

### Continuation Strategy
- Enters positions when price moves away from VWAP
- Looks for momentum confirmation through RSI and MACD
- Targets further movement in the same direction

### Mean Reversion Strategy
- Enters positions when price reaches extreme levels (2σ bands)
- Uses RSI and Stochastic for oversold/overbought confirmation
- Targets reversion back to VWAP

### Risk Management
- Position sizing based on stop-loss distance and risk percentage
- Maximum position size limits
- Daily loss limits and maximum drawdown protection
- Automatic stop-loss and take-profit execution

## Performance Analytics

The system provides comprehensive performance analysis:

- **Basic Metrics**: Win rate, profit factor, total return
- **Risk Metrics**: Maximum drawdown, Sharpe ratio, Calmar ratio
- **Strategy Analysis**: Performance by strategy type
- **Time Patterns**: Performance by hour, day, month
- **Interactive Dashboards**: Plotly-based visualizations

## Safety Features

- **Paper Trading Mode**: Test strategies without real money
- **Risk Limits**: Multiple layers of risk protection
- **Configuration Validation**: Prevents dangerous settings
- **Comprehensive Logging**: Full audit trail of all actions

## API Requirements

For live trading, you'll need:
- Binance API key and secret
- Futures trading enabled on your account
- Sufficient balance for margin requirements

For backtesting and paper trading, no API credentials are required.

## Disclaimer

This software is for educational and research purposes only. Trading cryptocurrencies involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Always test strategies thoroughly in paper trading mode before risking real capital.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests, please open an issue on GitHub.
