"""
Risk Management System
Handles position sizing, stop-loss, take-profit, and overall portfolio risk
"""
import pandas as pd
import numpy as np
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

class PositionType(Enum):
    """Position types"""
    LONG = "LONG"
    SHORT = "SHORT"

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    position_type: PositionType
    entry_price: float
    quantity: float
    entry_time: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    def update_current_price(self, price: float):
        """Update current price and calculate unrealized PnL"""
        self.current_price = price
        
        if self.position_type == PositionType.LONG:
            self.unrealized_pnl = (price - self.entry_price) * self.quantity
        else:  # SHORT
            self.unrealized_pnl = (self.entry_price - price) * self.quantity
    
    def get_position_value(self) -> float:
        """Get current position value in USD"""
        return abs(self.quantity * self.current_price)
    
    def should_stop_loss(self) -> bool:
        """Check if stop loss should be triggered"""
        if self.stop_loss is None:
            return False
            
        if self.position_type == PositionType.LONG:
            return self.current_price <= self.stop_loss
        else:  # SHORT
            return self.current_price >= self.stop_loss
    
    def should_take_profit(self) -> bool:
        """Check if take profit should be triggered"""
        if self.take_profit is None:
            return False
            
        if self.position_type == PositionType.LONG:
            return self.current_price >= self.take_profit
        else:  # SHORT
            return self.current_price <= self.take_profit

class RiskManager:
    """
    Risk management system for trading operations
    """
    
    def __init__(self, config: Dict):
        """
        Initialize risk manager
        
        Args:
            config: Risk management configuration
        """
        self.config = config
        
        # Risk parameters
        self.max_position_size_usd = config.get('max_position_size_usd', 1000.0)
        self.risk_per_trade_percent = config.get('risk_per_trade_percent', 2.0)
        self.max_leverage = config.get('max_leverage', 10.0)
        self.max_daily_loss_percent = config.get('max_daily_loss_percent', 5.0)
        self.max_drawdown_percent = config.get('max_drawdown_percent', 10.0)
        
        # Portfolio tracking
        self.account_balance = config.get('initial_balance', 10000.0)
        self.initial_balance = self.account_balance
        self.positions: Dict[str, Position] = {}
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.max_balance = self.account_balance
        self.trade_count = 0
        self.winning_trades = 0
        
        # Risk limits
        self.trading_enabled = True
        self.daily_loss_limit_hit = False
        self.max_drawdown_hit = False
        
    def calculate_position_size(self, 
                              entry_price: float, 
                              stop_loss: float, 
                              risk_amount: Optional[float] = None) -> float:
        """
        Calculate position size based on risk management rules
        
        Args:
            entry_price: Entry price for the position
            stop_loss: Stop loss price
            risk_amount: Custom risk amount (optional)
            
        Returns:
            Position size in base currency units
        """
        if risk_amount is None:
            risk_amount = self.account_balance * (self.risk_per_trade_percent / 100)
        
        # Calculate risk per unit
        risk_per_unit = abs(entry_price - stop_loss)
        
        if risk_per_unit == 0:
            return 0.0
        
        # Calculate position size
        position_size = risk_amount / risk_per_unit
        
        # Apply maximum position size limit
        max_position_value = min(
            self.max_position_size_usd,
            self.account_balance * 0.2  # Max 20% of account per position
        )
        
        max_position_size = max_position_value / entry_price
        position_size = min(position_size, max_position_size)
        
        # Apply leverage limit
        leveraged_value = position_size * entry_price
        if leveraged_value > self.account_balance * self.max_leverage:
            position_size = (self.account_balance * self.max_leverage) / entry_price
        
        return position_size
    
    def can_open_position(self, symbol: str, position_value: float) -> Tuple[bool, str]:
        """
        Check if a new position can be opened
        
        Args:
            symbol: Trading symbol
            position_value: Value of the position to open
            
        Returns:
            Tuple of (can_open, reason)
        """
        # Check if trading is enabled
        if not self.trading_enabled:
            return False, "Trading disabled due to risk limits"
        
        # Check daily loss limit
        if self.daily_loss_limit_hit:
            return False, "Daily loss limit reached"
        
        # Check maximum drawdown
        if self.max_drawdown_hit:
            return False, "Maximum drawdown limit reached"
        
        # Check if position already exists
        if symbol in self.positions:
            return False, f"Position already exists for {symbol}"
        
        # Check position size limits
        if position_value > self.max_position_size_usd:
            return False, f"Position size exceeds maximum limit"
        
        # Check account balance
        required_margin = position_value / self.max_leverage
        if required_margin > self.account_balance * 0.8:  # Keep 20% buffer
            return False, "Insufficient account balance"
        
        # Check total exposure
        total_exposure = sum(pos.get_position_value() for pos in self.positions.values())
        if total_exposure + position_value > self.account_balance * 3:  # Max 3x exposure
            return False, "Total exposure limit exceeded"
        
        return True, "Position can be opened"
    
    def open_position(self, 
                     symbol: str, 
                     position_type: PositionType, 
                     entry_price: float, 
                     quantity: float,
                     stop_loss: Optional[float] = None,
                     take_profit: Optional[float] = None) -> bool:
        """
        Open a new position
        
        Args:
            symbol: Trading symbol
            position_type: Long or short position
            entry_price: Entry price
            quantity: Position quantity
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            True if position opened successfully
        """
        position_value = quantity * entry_price
        
        can_open, reason = self.can_open_position(symbol, position_value)
        if not can_open:
            print(f"Cannot open position: {reason}")
            return False
        
        # Create position
        position = Position(
            symbol=symbol,
            position_type=position_type,
            entry_price=entry_price,
            quantity=quantity,
            entry_time=datetime.now(),
            stop_loss=stop_loss,
            take_profit=take_profit,
            current_price=entry_price
        )
        
        self.positions[symbol] = position
        self.trade_count += 1
        
        print(f"Opened {position_type.value} position for {symbol}: "
              f"Quantity={quantity:.6f}, Entry=${entry_price:.2f}")
        
        return True
    
    def close_position(self, symbol: str, exit_price: float, reason: str = "") -> Optional[float]:
        """
        Close an existing position
        
        Args:
            symbol: Trading symbol
            exit_price: Exit price
            reason: Reason for closing
            
        Returns:
            Realized PnL if position closed successfully
        """
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        
        # Calculate realized PnL
        if position.position_type == PositionType.LONG:
            realized_pnl = (exit_price - position.entry_price) * position.quantity
        else:  # SHORT
            realized_pnl = (position.entry_price - exit_price) * position.quantity
        
        # Update account balance
        self.account_balance += realized_pnl
        self.total_pnl += realized_pnl
        self.daily_pnl += realized_pnl
        
        # Track winning trades
        if realized_pnl > 0:
            self.winning_trades += 1
        
        # Update max balance
        if self.account_balance > self.max_balance:
            self.max_balance = self.account_balance
        
        print(f"Closed {position.position_type.value} position for {symbol}: "
              f"Exit=${exit_price:.2f}, PnL=${realized_pnl:.2f}, Reason={reason}")
        
        # Remove position
        del self.positions[symbol]
        
        return realized_pnl
    
    def update_positions(self, market_data: Dict[str, float]):
        """
        Update all positions with current market prices
        
        Args:
            market_data: Dictionary of symbol -> current_price
        """
        positions_to_close = []
        
        for symbol, position in self.positions.items():
            if symbol in market_data:
                current_price = market_data[symbol]
                position.update_current_price(current_price)
                
                # Check stop loss
                if position.should_stop_loss():
                    positions_to_close.append((symbol, current_price, "Stop Loss"))
                
                # Check take profit
                elif position.should_take_profit():
                    positions_to_close.append((symbol, current_price, "Take Profit"))
        
        # Close positions that hit stop loss or take profit
        for symbol, price, reason in positions_to_close:
            self.close_position(symbol, price, reason)
    
    def check_risk_limits(self):
        """Check and enforce risk limits"""
        # Check daily loss limit
        daily_loss_percent = (self.daily_pnl / self.initial_balance) * 100
        if daily_loss_percent <= -self.max_daily_loss_percent:
            self.daily_loss_limit_hit = True
            self.trading_enabled = False
            print(f"Daily loss limit hit: {daily_loss_percent:.2f}%")
        
        # Check maximum drawdown
        current_drawdown = ((self.max_balance - self.account_balance) / self.max_balance) * 100
        if current_drawdown >= self.max_drawdown_percent:
            self.max_drawdown_hit = True
            self.trading_enabled = False
            print(f"Maximum drawdown limit hit: {current_drawdown:.2f}%")
    
    def reset_daily_limits(self):
        """Reset daily limits (call at start of new trading day)"""
        self.daily_pnl = 0.0
        self.daily_loss_limit_hit = False
        if not self.max_drawdown_hit:
            self.trading_enabled = True
    
    def get_portfolio_summary(self) -> Dict:
        """Get portfolio summary statistics"""
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_position_value = sum(pos.get_position_value() for pos in self.positions.values())
        
        win_rate = (self.winning_trades / self.trade_count * 100) if self.trade_count > 0 else 0
        
        return {
            'account_balance': self.account_balance,
            'total_pnl': self.total_pnl,
            'daily_pnl': self.daily_pnl,
            'unrealized_pnl': total_unrealized_pnl,
            'total_position_value': total_position_value,
            'open_positions': len(self.positions),
            'trade_count': self.trade_count,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'max_drawdown': ((self.max_balance - self.account_balance) / self.max_balance) * 100,
            'trading_enabled': self.trading_enabled
        }
