"""
Performance Analytics Module
Analyzes trading performance, calculates metrics, and generates reports
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json

class PerformanceAnalyzer:
    """
    Comprehensive performance analysis for trading strategies
    """
    
    def __init__(self):
        self.trades_df = pd.DataFrame()
        self.equity_curve = pd.DataFrame()
        
    def load_trades(self, trades: List[Dict]) -> pd.DataFrame:
        """
        Load trades data for analysis
        
        Args:
            trades: List of trade dictionaries
            
        Returns:
            DataFrame with trades data
        """
        if not trades:
            return pd.DataFrame()
        
        self.trades_df = pd.DataFrame(trades)
        
        # Ensure required columns exist
        required_columns = ['entry_time', 'exit_time', 'entry_price', 'exit_price', 'pnl', 'position_type']
        for col in required_columns:
            if col not in self.trades_df.columns:
                self.trades_df[col] = np.nan
        
        # Convert datetime columns
        if 'entry_time' in self.trades_df.columns:
            self.trades_df['entry_time'] = pd.to_datetime(self.trades_df['entry_time'])
        if 'exit_time' in self.trades_df.columns:
            self.trades_df['exit_time'] = pd.to_datetime(self.trades_df['exit_time'])
        
        # Calculate additional metrics
        self._calculate_trade_metrics()
        
        return self.trades_df
    
    def _calculate_trade_metrics(self):
        """Calculate additional trade metrics"""
        if self.trades_df.empty:
            return
        
        # Trade duration
        if 'entry_time' in self.trades_df.columns and 'exit_time' in self.trades_df.columns:
            self.trades_df['duration_hours'] = (
                self.trades_df['exit_time'] - self.trades_df['entry_time']
            ).dt.total_seconds() / 3600
        
        # Return percentage
        if 'entry_price' in self.trades_df.columns and 'exit_price' in self.trades_df.columns:
            self.trades_df['return_pct'] = np.where(
                self.trades_df['position_type'] == 'LONG',
                (self.trades_df['exit_price'] - self.trades_df['entry_price']) / self.trades_df['entry_price'] * 100,
                (self.trades_df['entry_price'] - self.trades_df['exit_price']) / self.trades_df['entry_price'] * 100
            )
        
        # Win/Loss classification
        self.trades_df['is_winner'] = self.trades_df['pnl'] > 0
        
        # Cumulative PnL
        self.trades_df['cumulative_pnl'] = self.trades_df['pnl'].cumsum()
    
    def calculate_basic_metrics(self) -> Dict:
        """
        Calculate basic performance metrics
        
        Returns:
            Dictionary with basic metrics
        """
        if self.trades_df.empty:
            return {}
        
        total_trades = len(self.trades_df)
        winning_trades = len(self.trades_df[self.trades_df['is_winner']])
        losing_trades = total_trades - winning_trades
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        total_pnl = self.trades_df['pnl'].sum()
        avg_win = self.trades_df[self.trades_df['is_winner']]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = self.trades_df[~self.trades_df['is_winner']]['pnl'].mean() if losing_trades > 0 else 0
        
        largest_win = self.trades_df['pnl'].max() if total_trades > 0 else 0
        largest_loss = self.trades_df['pnl'].min() if total_trades > 0 else 0
        
        # Profit factor
        gross_profit = self.trades_df[self.trades_df['is_winner']]['pnl'].sum()
        gross_loss = abs(self.trades_df[~self.trades_df['is_winner']]['pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Average trade duration
        avg_duration = self.trades_df['duration_hours'].mean() if 'duration_hours' in self.trades_df.columns else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'profit_factor': profit_factor,
            'avg_duration_hours': avg_duration
        }
    
    def calculate_risk_metrics(self, initial_balance: float = 10000) -> Dict:
        """
        Calculate risk-adjusted performance metrics
        
        Args:
            initial_balance: Initial account balance
            
        Returns:
            Dictionary with risk metrics
        """
        if self.trades_df.empty:
            return {}
        
        # Calculate equity curve
        equity_curve = [initial_balance]
        for pnl in self.trades_df['pnl']:
            equity_curve.append(equity_curve[-1] + pnl)
        
        equity_series = pd.Series(equity_curve)
        
        # Maximum drawdown
        peak = equity_series.expanding().max()
        drawdown = (peak - equity_series) / peak * 100
        max_drawdown = drawdown.max()
        
        # Calmar ratio (annual return / max drawdown)
        total_return = (equity_series.iloc[-1] - equity_series.iloc[0]) / equity_series.iloc[0]
        
        # Estimate annualized return (assuming data represents reasonable time period)
        if 'entry_time' in self.trades_df.columns and len(self.trades_df) > 1:
            time_span = (self.trades_df['exit_time'].max() - self.trades_df['entry_time'].min()).days
            if time_span > 0:
                annualized_return = (1 + total_return) ** (365 / time_span) - 1
            else:
                annualized_return = 0
        else:
            annualized_return = 0
        
        calmar_ratio = annualized_return / (max_drawdown / 100) if max_drawdown > 0 else 0
        
        # Sharpe ratio (simplified)
        returns = self.trades_df['return_pct'] / 100 if 'return_pct' in self.trades_df.columns else self.trades_df['pnl'] / initial_balance
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
        
        # Sortino ratio (downside deviation)
        negative_returns = returns[returns < 0]
        downside_deviation = negative_returns.std() if len(negative_returns) > 0 else 0
        sortino_ratio = returns.mean() / downside_deviation if downside_deviation > 0 else 0
        
        # Recovery factor
        recovery_factor = total_return / (max_drawdown / 100) if max_drawdown > 0 else 0
        
        return {
            'max_drawdown': max_drawdown,
            'total_return': total_return * 100,
            'annualized_return': annualized_return * 100,
            'calmar_ratio': calmar_ratio,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'recovery_factor': recovery_factor
        }
    
    def analyze_strategy_performance(self) -> Dict:
        """
        Analyze performance by strategy type
        
        Returns:
            Dictionary with strategy-specific metrics
        """
        if self.trades_df.empty or 'strategy_type' not in self.trades_df.columns:
            return {}
        
        strategy_analysis = {}
        
        for strategy in self.trades_df['strategy_type'].unique():
            strategy_trades = self.trades_df[self.trades_df['strategy_type'] == strategy]
            
            if len(strategy_trades) > 0:
                total_trades = len(strategy_trades)
                winning_trades = len(strategy_trades[strategy_trades['is_winner']])
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                total_pnl = strategy_trades['pnl'].sum()
                avg_pnl = strategy_trades['pnl'].mean()
                
                strategy_analysis[strategy] = {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'win_rate': win_rate,
                    'total_pnl': total_pnl,
                    'avg_pnl': avg_pnl
                }
        
        return strategy_analysis
    
    def analyze_time_patterns(self) -> Dict:
        """
        Analyze performance patterns by time
        
        Returns:
            Dictionary with time-based analysis
        """
        if self.trades_df.empty or 'entry_time' not in self.trades_df.columns:
            return {}
        
        # Hour of day analysis
        self.trades_df['hour'] = self.trades_df['entry_time'].dt.hour
        hourly_performance = self.trades_df.groupby('hour')['pnl'].agg(['count', 'sum', 'mean']).to_dict()
        
        # Day of week analysis
        self.trades_df['day_of_week'] = self.trades_df['entry_time'].dt.day_name()
        daily_performance = self.trades_df.groupby('day_of_week')['pnl'].agg(['count', 'sum', 'mean']).to_dict()
        
        # Monthly analysis
        self.trades_df['month'] = self.trades_df['entry_time'].dt.month_name()
        monthly_performance = self.trades_df.groupby('month')['pnl'].agg(['count', 'sum', 'mean']).to_dict()
        
        return {
            'hourly': hourly_performance,
            'daily': daily_performance,
            'monthly': monthly_performance
        }
    
    def generate_performance_report(self, initial_balance: float = 10000) -> Dict:
        """
        Generate comprehensive performance report
        
        Args:
            initial_balance: Initial account balance
            
        Returns:
            Complete performance report
        """
        basic_metrics = self.calculate_basic_metrics()
        risk_metrics = self.calculate_risk_metrics(initial_balance)
        strategy_analysis = self.analyze_strategy_performance()
        time_patterns = self.analyze_time_patterns()
        
        return {
            'basic_metrics': basic_metrics,
            'risk_metrics': risk_metrics,
            'strategy_analysis': strategy_analysis,
            'time_patterns': time_patterns,
            'generated_at': datetime.now().isoformat()
        }
    
    def create_performance_dashboard(self, initial_balance: float = 10000) -> go.Figure:
        """
        Create interactive performance dashboard
        
        Args:
            initial_balance: Initial account balance
            
        Returns:
            Plotly figure with performance dashboard
        """
        if self.trades_df.empty:
            return go.Figure()
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Equity Curve', 'PnL Distribution', 'Win Rate by Strategy', 'Monthly Performance'),
            specs=[[{"secondary_y": True}, {"type": "histogram"}],
                   [{"type": "bar"}, {"type": "bar"}]]
        )
        
        # Equity curve
        equity_curve = [initial_balance]
        for pnl in self.trades_df['pnl']:
            equity_curve.append(equity_curve[-1] + pnl)
        
        fig.add_trace(
            go.Scatter(
                x=list(range(len(equity_curve))),
                y=equity_curve,
                name='Equity Curve',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        # Drawdown
        equity_series = pd.Series(equity_curve)
        peak = equity_series.expanding().max()
        drawdown = (peak - equity_series) / peak * 100
        
        fig.add_trace(
            go.Scatter(
                x=list(range(len(drawdown))),
                y=-drawdown,
                name='Drawdown %',
                line=dict(color='red'),
                fill='tonexty'
            ),
            row=1, col=1, secondary_y=True
        )
        
        # PnL distribution
        fig.add_trace(
            go.Histogram(
                x=self.trades_df['pnl'],
                name='PnL Distribution',
                nbinsx=20
            ),
            row=1, col=2
        )
        
        # Win rate by strategy
        if 'strategy_type' in self.trades_df.columns:
            strategy_stats = self.trades_df.groupby('strategy_type').agg({
                'is_winner': ['count', 'sum']
            }).round(2)
            
            strategy_stats.columns = ['total_trades', 'winning_trades']
            strategy_stats['win_rate'] = (strategy_stats['winning_trades'] / strategy_stats['total_trades'] * 100)
            
            fig.add_trace(
                go.Bar(
                    x=strategy_stats.index,
                    y=strategy_stats['win_rate'],
                    name='Win Rate %'
                ),
                row=2, col=1
            )
        
        # Monthly performance
        if 'entry_time' in self.trades_df.columns:
            self.trades_df['month'] = self.trades_df['entry_time'].dt.to_period('M')
            monthly_pnl = self.trades_df.groupby('month')['pnl'].sum()
            
            fig.add_trace(
                go.Bar(
                    x=[str(m) for m in monthly_pnl.index],
                    y=monthly_pnl.values,
                    name='Monthly PnL'
                ),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            title='Trading Performance Dashboard',
            height=600,
            showlegend=True
        )
        
        return fig
    
    def save_report(self, report: Dict, filename: str = None):
        """
        Save performance report to file
        
        Args:
            report: Performance report dictionary
            filename: Output filename
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Performance report saved to {filename}")
