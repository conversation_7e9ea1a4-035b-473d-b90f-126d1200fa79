"""
Trade Execution Engine
Handles real-time signal generation and trade execution with paper trading mode
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Callable
import pandas as pd
from loguru import logger
import json

from data.binance_client import BinanceDataFetcher
from strategies.vwap_strategy import VWAPStrategy, TradingSignal, SignalType
from risk_management.risk_manager import RiskManager, PositionType
from indicators.vwap import VWAPCalculator
from indicators.technical_indicators import TechnicalIndicators

class PaperTradingExecutor:
    """
    Paper trading executor for testing strategies without real money
    """
    
    def __init__(self, initial_balance: float = 10000.0):
        self.balance = initial_balance
        self.positions = {}
        self.trade_history = []
        
    def execute_order(self, symbol: str, side: str, quantity: float, price: float) -> Dict:
        """Execute paper trading order"""
        order_id = f"paper_{int(time.time() * 1000)}"
        
        order = {
            'order_id': order_id,
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'price': price,
            'timestamp': datetime.now(),
            'status': 'FILLED'
        }
        
        self.trade_history.append(order)
        logger.info(f"Paper trade executed: {side} {quantity} {symbol} @ ${price}")
        
        return order

class LiveTradingExecutor:
    """
    Live trading executor using Binance API
    """
    
    def __init__(self, binance_client):
        self.client = binance_client
        
    def execute_order(self, symbol: str, side: str, quantity: float, order_type: str = "MARKET") -> Dict:
        """Execute live trading order"""
        try:
            if self.client and self.client.client:
                # This would execute real trades - implement with caution
                # order = self.client.client.futures_create_order(
                #     symbol=symbol,
                #     side=side,
                #     type=order_type,
                #     quantity=quantity
                # )
                # return order
                pass
            
            logger.warning("Live trading not implemented - use paper trading mode")
            return {}
            
        except Exception as e:
            logger.error(f"Error executing live order: {e}")
            return {}

class TradeExecutor:
    """
    Main trade execution engine
    """
    
    def __init__(self, config: Dict):
        """
        Initialize trade executor
        
        Args:
            config: Trading system configuration
        """
        self.config = config
        self.symbol = config['binance']['symbol']
        self.paper_trading = config['system']['paper_trading']
        
        # Initialize components
        self.data_fetcher = BinanceDataFetcher(
            api_key=config['binance']['api_key'],
            api_secret=config['binance']['api_secret'],
            testnet=config['binance']['testnet']
        )
        
        self.vwap_calculator = VWAPCalculator(
            std_dev_multipliers=[
                config['trading']['std_dev_multiplier_1'],
                config['trading']['std_dev_multiplier_2'],
                config['trading']['std_dev_multiplier_3']
            ]
        )
        
        self.strategy = VWAPStrategy(config['trading'])
        self.risk_manager = RiskManager(config['trading'])
        
        # Initialize executor
        if self.paper_trading:
            self.executor = PaperTradingExecutor(config['trading']['initial_balance'])
        else:
            self.executor = LiveTradingExecutor(self.data_fetcher)
        
        # Data management
        self.historical_data = pd.DataFrame()
        self.current_data = pd.DataFrame()
        self.last_signal_time = None
        
        # Callbacks
        self.signal_callbacks: List[Callable] = []
        self.trade_callbacks: List[Callable] = []
        
        # State
        self.is_running = False
        self.last_update = None
        
    def add_signal_callback(self, callback: Callable):
        """Add callback for signal events"""
        self.signal_callbacks.append(callback)
        
    def add_trade_callback(self, callback: Callable):
        """Add callback for trade events"""
        self.trade_callbacks.append(callback)
    
    def load_historical_data(self, days: int = 30) -> bool:
        """
        Load historical data for strategy initialization
        
        Args:
            days: Number of days of historical data to load
            
        Returns:
            True if data loaded successfully
        """
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"Loading {days} days of historical data...")
            
            self.historical_data = self.data_fetcher.get_extended_historical_data(
                symbol=self.symbol,
                interval=self.config['trading']['primary_timeframe'],
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d")
            )
            
            if not self.historical_data.empty:
                # Prepare data with indicators
                self.current_data = self._prepare_data(self.historical_data)
                logger.info(f"Loaded {len(self.historical_data)} historical data points")
                return True
            else:
                logger.error("Failed to load historical data")
                return False
                
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return False
    
    def _prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare data with all indicators
        
        Args:
            df: Raw OHLCV data
            
        Returns:
            DataFrame with indicators
        """
        # Calculate VWAP
        df_with_vwap = self.vwap_calculator.calculate_vwap(df)
        
        # Calculate technical indicators
        indicator_config = {
            'rsi_period': self.config['trading']['rsi_period'],
            'ema_fast': self.config['trading']['ema_fast_period'],
            'ema_slow': self.config['trading']['ema_slow_period'],
            'macd_signal': self.config['trading']['macd_signal_period']
        }
        
        df_with_indicators = TechnicalIndicators.calculate_all_indicators(
            df_with_vwap, indicator_config
        )
        
        return df_with_indicators
    
    def _on_new_kline(self, kline_data: Dict):
        """
        Handle new kline data from WebSocket stream
        
        Args:
            kline_data: New kline data
        """
        try:
            # Only process closed klines
            if not kline_data.get('is_closed', False):
                return
            
            # Create new row
            new_row = pd.DataFrame([{
                'open': kline_data['open'],
                'high': kline_data['high'],
                'low': kline_data['low'],
                'close': kline_data['close'],
                'volume': kline_data['volume']
            }], index=[kline_data['timestamp']])
            
            # Update historical data
            self.historical_data = pd.concat([self.historical_data, new_row])
            
            # Keep only recent data (last 1000 points)
            if len(self.historical_data) > 1000:
                self.historical_data = self.historical_data.tail(1000)
            
            # Recalculate indicators
            self.current_data = self._prepare_data(self.historical_data)
            
            # Update positions
            current_price = kline_data['close']
            self.risk_manager.update_positions({self.symbol: current_price})
            
            # Check for new signals
            self._check_signals()
            
            self.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"Error processing new kline: {e}")
    
    def _check_signals(self):
        """Check for new trading signals"""
        try:
            if self.current_data.empty or len(self.current_data) < 21:
                return
            
            # Get current signal
            signal = self.strategy.get_current_signal(self.current_data)
            
            if signal and signal.confidence > 0.6:
                # Avoid duplicate signals
                if (self.last_signal_time and 
                    signal.timestamp - self.last_signal_time < timedelta(minutes=5)):
                    return
                
                self.last_signal_time = signal.timestamp
                
                # Notify callbacks
                for callback in self.signal_callbacks:
                    try:
                        callback(signal)
                    except Exception as e:
                        logger.error(f"Error in signal callback: {e}")
                
                # Execute trade if no position exists
                if self.symbol not in self.risk_manager.positions:
                    self._execute_signal(signal)
                    
        except Exception as e:
            logger.error(f"Error checking signals: {e}")
    
    def _execute_signal(self, signal: TradingSignal):
        """
        Execute a trading signal
        
        Args:
            signal: Trading signal to execute
        """
        try:
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                signal.price, signal.stop_loss
            )
            
            if position_size <= 0:
                logger.warning("Position size is zero or negative")
                return
            
            # Determine position type and side
            if signal.signal_type == SignalType.LONG:
                position_type = PositionType.LONG
                side = "BUY"
            elif signal.signal_type == SignalType.SHORT:
                position_type = PositionType.SHORT
                side = "SELL"
            else:
                logger.warning(f"Unknown signal type: {signal.signal_type}")
                return
            
            # Check if position can be opened
            can_open, reason = self.risk_manager.can_open_position(
                self.symbol, position_size * signal.price
            )
            
            if not can_open:
                logger.warning(f"Cannot open position: {reason}")
                return
            
            # Execute order
            order = self.executor.execute_order(
                symbol=self.symbol,
                side=side,
                quantity=position_size,
                price=signal.price
            )
            
            if order:
                # Open position in risk manager
                success = self.risk_manager.open_position(
                    symbol=self.symbol,
                    position_type=position_type,
                    entry_price=signal.price,
                    quantity=position_size,
                    stop_loss=signal.stop_loss,
                    take_profit=signal.take_profit
                )
                
                if success:
                    trade_info = {
                        'signal': signal,
                        'order': order,
                        'position_size': position_size
                    }
                    
                    # Notify callbacks
                    for callback in self.trade_callbacks:
                        try:
                            callback(trade_info)
                        except Exception as e:
                            logger.error(f"Error in trade callback: {e}")
                    
                    logger.info(f"Trade executed: {side} {position_size:.6f} {self.symbol}")
                
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
    
    def start_real_time_trading(self):
        """Start real-time trading"""
        if self.is_running:
            logger.warning("Trading already running")
            return
        
        if self.current_data.empty:
            logger.error("No historical data loaded. Call load_historical_data() first.")
            return
        
        logger.info("Starting real-time trading...")
        self.is_running = True
        
        # Start WebSocket stream
        self.data_fetcher.start_kline_stream(
            symbol=self.symbol,
            interval=self.config['trading']['signal_timeframe'],
            callback=self._on_new_kline
        )
        
        logger.info("Real-time trading started")
    
    def stop_trading(self):
        """Stop real-time trading"""
        if not self.is_running:
            return
        
        logger.info("Stopping real-time trading...")
        self.is_running = False
        
        # Stop WebSocket stream
        self.data_fetcher.stop_stream()
        
        logger.info("Real-time trading stopped")
    
    def get_status(self) -> Dict:
        """Get current trading status"""
        portfolio_summary = self.risk_manager.get_portfolio_summary()
        
        return {
            'is_running': self.is_running,
            'last_update': self.last_update,
            'data_points': len(self.current_data),
            'portfolio': portfolio_summary,
            'current_price': self.data_fetcher.get_current_price(self.symbol) if self.data_fetcher else 0,
            'paper_trading': self.paper_trading
        }
