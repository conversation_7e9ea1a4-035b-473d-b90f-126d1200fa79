"""
Main Application for VWAP Trading System
Entry point for backtesting and live trading
"""
import argparse
import sys
import os
from datetime import datetime, timedelta
from loguru import logger
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from data.binance_client import BinanceDataFetcher
from backtesting.backtest_engine import BacktestEngine
from execution.trade_executor import TradeExecutor
from analytics.performance_analyzer import PerformanceAnalyzer

def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Console logging
    logger.add(
        sys.stdout,
        level=config.system.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # File logging
    logger.add(
        "logs/trading_system.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="30 days"
    )

def run_backtest(args):
    """Run backtesting"""
    logger.info("Starting backtest...")
    
    # Validate configuration
    config.validate_config()
    
    # Initialize data fetcher
    data_fetcher = BinanceDataFetcher(testnet=True)
    
    # Load historical data
    logger.info(f"Loading historical data from {args.start_date} to {args.end_date}")
    
    historical_data = data_fetcher.get_extended_historical_data(
        symbol=config.binance.symbol,
        interval=config.trading.primary_timeframe,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    if historical_data.empty:
        logger.error("No historical data available")
        return
    
    logger.info(f"Loaded {len(historical_data)} data points")
    
    # Initialize backtest engine
    backtest_engine = BacktestEngine(config.to_dict())
    
    # Run backtest
    results = backtest_engine.run_backtest(
        historical_data,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    if "error" in results:
        logger.error(f"Backtest failed: {results['error']}")
        return
    
    # Display results
    print("\n" + "="*50)
    print("BACKTEST RESULTS")
    print("="*50)
    print(f"Initial Balance: ${results['initial_balance']:,.2f}")
    print(f"Final Balance: ${results['final_balance']:,.2f}")
    print(f"Total Return: {results['total_return']:.2f}%")
    print(f"Total Trades: {results['total_trades']}")
    print(f"Win Rate: {results['win_rate']:.2f}%")
    print(f"Profit Factor: {results['profit_factor']:.2f}")
    print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    print("="*50)
    
    # Generate performance analysis
    analyzer = PerformanceAnalyzer()
    analyzer.load_trades(results['trades'])
    
    performance_report = analyzer.generate_performance_report(results['initial_balance'])
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save backtest results
    results_filename = f"backtest_results_{timestamp}.json"
    import json
    with open(results_filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Save performance report
    analyzer.save_report(performance_report, f"performance_report_{timestamp}.json")
    
    # Create and save plots
    if args.plot:
        logger.info("Generating plots...")
        
        # Backtest plot
        backtest_plot = backtest_engine.plot_results(historical_data, results)
        backtest_plot.write_html(f"backtest_plot_{timestamp}.html")
        
        # Performance dashboard
        dashboard = analyzer.create_performance_dashboard(results['initial_balance'])
        dashboard.write_html(f"performance_dashboard_{timestamp}.html")
        
        logger.info(f"Plots saved as HTML files")
    
    logger.info(f"Backtest completed. Results saved to {results_filename}")

def run_live_trading(args):
    """Run live trading"""
    logger.info("Starting live trading...")
    
    # Validate configuration
    config.validate_config()
    
    if not config.system.paper_trading and not args.live:
        logger.error("Live trading requires --live flag for safety")
        return
    
    # Initialize trade executor
    trade_executor = TradeExecutor(config.to_dict())
    
    # Load historical data for strategy initialization
    logger.info("Loading historical data for strategy initialization...")
    success = trade_executor.load_historical_data(days=30)
    
    if not success:
        logger.error("Failed to load historical data")
        return
    
    # Setup callbacks
    def on_signal(signal):
        logger.info(f"New signal: {signal.signal_type.value} {signal.strategy_type.value} "
                   f"@ ${signal.price:.2f} (confidence: {signal.confidence:.2f})")
    
    def on_trade(trade_info):
        logger.info(f"Trade executed: {trade_info['order']}")
    
    trade_executor.add_signal_callback(on_signal)
    trade_executor.add_trade_callback(on_trade)
    
    # Start trading
    try:
        trade_executor.start_real_time_trading()
        
        logger.info("Trading started. Press Ctrl+C to stop.")
        
        # Keep running and display status
        import time
        while True:
            time.sleep(30)  # Update every 30 seconds
            
            status = trade_executor.get_status()
            logger.info(f"Status - Running: {status['is_running']}, "
                       f"Balance: ${status['portfolio']['account_balance']:.2f}, "
                       f"Open Positions: {status['portfolio']['open_positions']}")
            
    except KeyboardInterrupt:
        logger.info("Stopping trading...")
        trade_executor.stop_trading()
        
        # Display final status
        final_status = trade_executor.get_status()
        print("\n" + "="*50)
        print("TRADING SESSION SUMMARY")
        print("="*50)
        print(f"Final Balance: ${final_status['portfolio']['account_balance']:,.2f}")
        print(f"Total PnL: ${final_status['portfolio']['total_pnl']:,.2f}")
        print(f"Total Trades: {final_status['portfolio']['trade_count']}")
        print(f"Win Rate: {final_status['portfolio']['win_rate']:.2f}%")
        print("="*50)

def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description="VWAP Trading System")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Backtest command
    backtest_parser = subparsers.add_parser('backtest', help='Run backtesting')
    backtest_parser.add_argument('--start-date', default='2023-01-01', 
                                help='Start date for backtest (YYYY-MM-DD)')
    backtest_parser.add_argument('--end-date', default='2024-01-01',
                                help='End date for backtest (YYYY-MM-DD)')
    backtest_parser.add_argument('--plot', action='store_true',
                                help='Generate plots')
    
    # Live trading command
    live_parser = subparsers.add_parser('live', help='Run live trading')
    live_parser.add_argument('--live', action='store_true',
                            help='Enable live trading (required for non-paper trading)')
    
    # Data command
    data_parser = subparsers.add_parser('data', help='Data operations')
    data_parser.add_argument('--download', action='store_true',
                            help='Download historical data')
    data_parser.add_argument('--days', type=int, default=30,
                            help='Number of days to download')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    if args.command == 'backtest':
        run_backtest(args)
    elif args.command == 'live':
        run_live_trading(args)
    elif args.command == 'data':
        if args.download:
            logger.info(f"Downloading {args.days} days of historical data...")
            data_fetcher = BinanceDataFetcher(testnet=True)
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=args.days)
            
            data = data_fetcher.get_extended_historical_data(
                symbol=config.binance.symbol,
                interval=config.trading.primary_timeframe,
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d")
            )
            
            if not data.empty:
                filename = f"data/{config.binance.symbol}_{config.trading.primary_timeframe}_{args.days}d.csv"
                data.to_csv(filename)
                logger.info(f"Data saved to {filename}")
            else:
                logger.error("Failed to download data")
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
