"""
Binance API Client for fetching Bitcoin USDT perpetual futures data
"""
import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from binance.client import Client
from binance.exceptions import BinanceAPIException
from loguru import logger
import asyncio
import websocket
import json
import threading

class BinanceDataFetcher:
    """
    Binance data fetcher for Bitcoin USDT perpetual futures
    Handles both historical and real-time data
    """
    
    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        """
        Initialize Binance client
        
        Args:
            api_key: Binance API key (optional for public data)
            api_secret: Binance API secret (optional for public data)
            testnet: Use testnet environment
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        # Initialize client for authenticated requests (if credentials provided)
        if api_key and api_secret:
            self.client = Client(api_key, api_secret, testnet=testnet)
        else:
            self.client = None
            
        # Base URLs
        if testnet:
            self.base_url = "https://testnet.binancefuture.com"
            self.ws_base_url = "wss://stream.binancefuture.com"
        else:
            self.base_url = "https://fapi.binance.com"
            self.ws_base_url = "wss://fstream.binance.com"
            
        # WebSocket connection
        self.ws = None
        self.ws_thread = None
        self.is_streaming = False
        self.stream_callbacks = {}
        
    def get_historical_klines(self, 
                            symbol: str = "BTCUSDT",
                            interval: str = "1h",
                            start_time: Optional[str] = None,
                            end_time: Optional[str] = None,
                            limit: int = 1000) -> pd.DataFrame:
        """
        Fetch historical kline/candlestick data
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            interval: Kline interval ('1m', '5m', '15m', '1h', '4h', '1d', etc.)
            start_time: Start time (ISO format or timestamp)
            end_time: End time (ISO format or timestamp)
            limit: Number of klines to fetch (max 1500)
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Convert time strings to timestamps if provided
            start_ts = None
            end_ts = None
            
            if start_time:
                if isinstance(start_time, str):
                    start_ts = int(pd.to_datetime(start_time).timestamp() * 1000)
                else:
                    start_ts = start_time
                    
            if end_time:
                if isinstance(end_time, str):
                    end_ts = int(pd.to_datetime(end_time).timestamp() * 1000)
                else:
                    end_ts = end_time
            
            # Fetch data using public API
            url = f"{self.base_url}/fapi/v1/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            if start_ts:
                params['startTime'] = start_ts
            if end_ts:
                params['endTime'] = end_ts
                
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Convert to DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert data types
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
            
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 
                             'quote_asset_volume', 'number_of_trades',
                             'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume']
            
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Set timestamp as index
            df.set_index('timestamp', inplace=True)
            
            # Keep only essential columns
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            logger.info(f"Fetched {len(df)} klines for {symbol} ({interval})")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return pd.DataFrame()
    
    def get_extended_historical_data(self,
                                   symbol: str = "BTCUSDT",
                                   interval: str = "1h",
                                   start_date: str = "2023-01-01",
                                   end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Fetch extended historical data by making multiple API calls
        
        Args:
            symbol: Trading symbol
            interval: Kline interval
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format), defaults to today
            
        Returns:
            DataFrame with extended historical data
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        all_data = []
        current_start = start_dt
        
        # Calculate interval duration for chunking
        interval_minutes = {
            '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720,
            '1d': 1440, '3d': 4320, '1w': 10080, '1M': 43200
        }
        
        minutes_per_interval = interval_minutes.get(interval, 60)
        max_klines = 1500  # Binance limit
        chunk_duration = timedelta(minutes=minutes_per_interval * max_klines)
        
        while current_start < end_dt:
            current_end = min(current_start + chunk_duration, end_dt)
            
            logger.info(f"Fetching data from {current_start} to {current_end}")
            
            chunk_data = self.get_historical_klines(
                symbol=symbol,
                interval=interval,
                start_time=current_start.isoformat(),
                end_time=current_end.isoformat(),
                limit=max_klines
            )
            
            if not chunk_data.empty:
                all_data.append(chunk_data)
            
            current_start = current_end
            time.sleep(0.1)  # Rate limiting
        
        if all_data:
            combined_df = pd.concat(all_data)
            combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
            combined_df.sort_index(inplace=True)
            
            logger.info(f"Total historical data: {len(combined_df)} records")
            return combined_df
        else:
            return pd.DataFrame()
    
    def get_current_price(self, symbol: str = "BTCUSDT") -> float:
        """
        Get current price for symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Current price
        """
        try:
            url = f"{self.base_url}/fapi/v1/ticker/price"
            params = {'symbol': symbol}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return float(data['price'])
            
        except Exception as e:
            logger.error(f"Error fetching current price: {e}")
            return 0.0
    
    def get_24hr_ticker(self, symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """
        Get 24hr ticker statistics
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with ticker data
        """
        try:
            url = f"{self.base_url}/fapi/v1/ticker/24hr"
            params = {'symbol': symbol}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Error fetching 24hr ticker: {e}")
            return {}
    
    def start_kline_stream(self, symbol: str = "BTCUSDT", interval: str = "1m", callback=None):
        """
        Start real-time kline stream
        
        Args:
            symbol: Trading symbol
            interval: Kline interval
            callback: Callback function to handle new kline data
        """
        if self.is_streaming:
            logger.warning("Stream already running")
            return
            
        stream_name = f"{symbol.lower()}@kline_{interval}"
        self.stream_callbacks[stream_name] = callback
        
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if 'k' in data:
                    kline_data = data['k']
                    
                    # Convert to standard format
                    formatted_data = {
                        'timestamp': pd.to_datetime(kline_data['t'], unit='ms'),
                        'open': float(kline_data['o']),
                        'high': float(kline_data['h']),
                        'low': float(kline_data['l']),
                        'close': float(kline_data['c']),
                        'volume': float(kline_data['v']),
                        'is_closed': kline_data['x']  # True if kline is closed
                    }
                    
                    if callback:
                        callback(formatted_data)
                        
            except Exception as e:
                logger.error(f"Error processing stream message: {e}")
        
        def on_error(ws, error):
            logger.error(f"WebSocket error: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            logger.info("WebSocket connection closed")
            self.is_streaming = False
        
        def on_open(ws):
            logger.info(f"Started kline stream for {symbol} ({interval})")
            self.is_streaming = True
        
        # Start WebSocket connection
        ws_url = f"{self.ws_base_url}/ws/{stream_name}"
        
        self.ws = websocket.WebSocketApp(
            ws_url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open
        )
        
        # Run in separate thread
        self.ws_thread = threading.Thread(target=self.ws.run_forever)
        self.ws_thread.daemon = True
        self.ws_thread.start()
    
    def stop_stream(self):
        """Stop the WebSocket stream"""
        if self.ws:
            self.ws.close()
        self.is_streaming = False
        logger.info("Stopped WebSocket stream")
